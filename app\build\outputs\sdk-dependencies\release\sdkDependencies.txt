# List of SDK dependencies of this app, this information is also included in an encrypted form in the APK.
# For more information visit: https://d.android.com/r/tools/dependency-metadata

library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib"
    version: "2.0.0"
  }
  digests {
    sha256: "$\t8\304\252\270\347>\210\207\003\343\347\323\370s\203\377\345\275Smm^<\020\rL\3207\237\317"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains"
    artifactId: "annotations"
    version: "23.0.0"
  }
  digests {
    sha256: "{\017\031r@\202\313\374\274f\345\253\352+\233\311,\360\212\036\241\036\031\0313\355C\200\036\263\315\005"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-jdk8"
    version: "1.9.10"
  }
  digests {
    sha256: "\244\307M\224\326L\341\253\3457`\376\003\211\335\224\037o\305X\320\332\263^G\300\205\241\036\310\017("
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-jdk7"
    version: "1.9.10"
  }
  digests {
    sha256: "\254ca\277\232\321\3558,!\003\331q,G\315\354\026b2\264\220>\325\226\350\207k\006\201\311\267"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-common"
    version: "2.0.0"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core-ktx"
    version: "1.15.0"
  }
  digests {
    sha256: "\307,\227\377J20\213\312\363\006\036\352]\3373E\\\344\023\316\344\252\035\\\202D\fWY\324\274"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation"
    version: "1.8.1"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation-jvm"
    version: "1.8.1"
  }
  digests {
    sha256: "\232\2532m\224\222\200\t\221\205C`\254$\217I<\347\367\303\0305\0310\233x\254\351\342@\366\366"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core"
    version: "1.15.0"
  }
  digests {
    sha256: "C+\205\241\227@v\341KH~\316J(\305\232\204\361\271\357\303\374\213\347,\327\360]2\005^Q"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation-experimental"
    version: "1.4.1"
  }
  digests {
    sha256: "k\324\307\307Go\202`\315;\333\270\021\203X>\223\374\237y\f\'\336\247\3341A\201\313\370z\240"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.collection"
    artifactId: "collection"
    version: "1.4.2"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.collection"
    artifactId: "collection-jvm"
    version: "1.4.2"
  }
  digests {
    sha256: "\230L\351\275x\000U\352\373\212\020Z\312?QF\206\277{\033br\354|dZ\230\316@\374}\264"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.collection"
    artifactId: "collection-ktx"
    version: "1.4.2"
  }
  digests {
    sha256: "\306\336\255\242\372\305;\216\246R=\275\247u\227\261(\000ftao\024\017\004\337#&Lm\032\243"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.concurrent"
    artifactId: "concurrent-futures"
    version: "1.1.0"
  }
  digests {
    sha256: "\f\340g\305\024\240\321\004\235\033\353\337p\2364N\323&o\351tBuh)7\315\313\0233N\236"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.guava"
    artifactId: "listenablefuture"
    version: "1.0"
  }
  digests {
    sha256: "\344\255v\a\345\300G|o\211\016\362jI\313\215\033\264\337\373e\v\253E\002\257\356ddN0i"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.interpolator"
    artifactId: "interpolator"
    version: "1.0.0"
  }
  digests {
    sha256: "3\03115\246O\342\037\242\303^\354f\210\361\247nQ&\006\300\374\203\334\033h\2367\255\327s*"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime"
    version: "2.8.7"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime-android"
    version: "2.8.7"
  }
  digests {
    sha256: "\270\353\231}\3244\327\016\271|\275m\023\225\2663|gqq|z\315Y\361\034\bS\363\206\002\204"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.arch.core"
    artifactId: "core-common"
    version: "2.2.0"
  }
  digests {
    sha256: "e0\212\006\261\300\016\341\206\313\236\0312\023\203\360C\271\223\201?\025\"\304\177J>3\003\275\272A"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.arch.core"
    artifactId: "core-runtime"
    version: "2.2.0"
  }
  digests {
    sha256: "\241\276^\f\252+\ab8b\257j\342\033:\260q\201#$Q\204\320\343\r\352\201\265?\231\nG"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-common"
    version: "2.8.7"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-common-jvm"
    version: "2.8.7"
  }
  digests {
    sha256: "a\310s\2472|\224n\3003\303\020\273\230\363\371.\352\274\355\340\341\245 \n\270\241\211d\203\307\277"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-core"
    version: "1.7.3"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-core-jvm"
    version: "1.7.3"
  }
  digests {
    sha256: "\032\263\254\303\217>sU\304\371\321\354b\020zF\372s\310\231\363\a\r\005^]Cs\337\346~\022"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-bom"
    version: "1.7.3"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-android"
    version: "1.7.3"
  }
  digests {
    sha256: "Y\377\373&\276\341,2\332\334\372]B\f*}\270]2SQ\201(\261p\357\332rf\023%m"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-common-java8"
    version: "2.8.7"
  }
  digests {
    sha256: "\306\336\255\242\372\305;\216\246R=\275\247u\227\261(\000ftao\024\017\004\337#&Lm\032\243"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata-core"
    version: "2.8.7"
  }
  digests {
    sha256: "\327\206\345\346\367\233\217\253\002o\t\242\324\256=\341\327(u\367dZk{\344\217\035\232\263\204sK"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-process"
    version: "2.8.7"
  }
  digests {
    sha256: "b\340v\242\336-e\n#\277yIT\031\370\2445z\330\351\262\215\200\023\030\320\374\335n5\372\207"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.startup"
    artifactId: "startup-runtime"
    version: "1.1.1"
  }
  digests {
    sha256: "\340\2462\2327\022b\376LE\003r\267\017\332\363;v\236\366\221p\224r7\207\317\316\211k\035\323"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.tracing"
    artifactId: "tracing"
    version: "1.2.0"
  }
  digests {
    sha256: "o\252\2209\r\037\333\360\255\271\251\233\371\235\346{\224\306\306\363Z\352\225\020Y:\235\027\22776\242"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime-ktx"
    version: "2.8.7"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime-ktx-android"
    version: "2.8.7"
  }
  digests {
    sha256: "\263\323\234]\272^ ~\364\307\235I\032vP\001\224\305\352ht\025\177\005\241_\001y,\f\304\347"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel"
    version: "2.8.7"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-android"
    version: "2.8.7"
  }
  digests {
    sha256: "\347\347\004?f\260wcuc\377VJ\177\306\316\344a\322\204\240\362H\\\005\n!C\253\275\211 "
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-ktx"
    version: "2.8.7"
  }
  digests {
    sha256: "\032\316\273P\337/\f\a\363y\325\342:(\367yOp\357\262\266\3104\254\353\350\303\224xN\346\324"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-savedstate"
    version: "2.8.7"
  }
  digests {
    sha256: "do*B\322\210K\0020\217\261\2307\b\236&i\203_\342\034\246\245\266\006f\325\353\224\320\201\335"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.savedstate"
    artifactId: "savedstate"
    version: "1.2.1"
  }
  digests {
    sha256: "!\247\324\274\366\275\271J\327\271(8\001R\223\000\264\373\270\200\214\244\361\221\340\315\316o\330\344pZ"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.savedstate"
    artifactId: "savedstate-ktx"
    version: "1.2.1"
  }
  digests {
    sha256: "\205S\370~q6\302N\305$5`\364\217\0342\313\245m\252wr/\211X\232\\\257\313\217x\224"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.profileinstaller"
    artifactId: "profileinstaller"
    version: "1.4.0"
  }
  digests {
    sha256: "\325\002\024\037\314\351\002C\017b\266t\303*\354\320\367Rb\347\356,\321\\t\255\266\027\315\023\023\n"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.versionedparcelable"
    artifactId: "versionedparcelable"
    version: "1.1.1"
  }
  digests {
    sha256: "W\350\3312`\321\215[\220\a\311\356\323\306J\321Y\336\220\310`\236\277\307J4|\275QE5\244"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.activity"
    artifactId: "activity-compose"
    version: "1.10.1"
  }
  digests {
    sha256: "\370\232\361\262l\314\203F48|\205|-\324\364eM7e\b\220\003\234R|/\355\a\333ja"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.activity"
    artifactId: "activity-ktx"
    version: "1.10.1"
  }
  digests {
    sha256: "\363\226\365\215\275w,\006[\2076\226\267J=M\buT\263vb\200\346;I\262]\273S\253\026"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.activity"
    artifactId: "activity"
    version: "1.10.1"
  }
  digests {
    sha256: "\266+R\214\221}\351\276I~\266\370\2100\031| \351\322\022g\303\221la4\222\345\356\203}M"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core-viewtree"
    version: "1.0.0"
  }
  digests {
    sha256: "\334\033g\215X\353\317+\372\025\207\276h\377\202f\224\316=\"\022Q\271\3570\324\324\263b\227\346\336"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.runtime"
    artifactId: "runtime"
    version: "1.7.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.runtime"
    artifactId: "runtime-android"
    version: "1.7.0"
  }
  digests {
    sha256: "E\334cRLpZ\266\032\265g\223\236\265\v\233\204\005\234P]\ak+\337-\312&hJ\000\002"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.runtime"
    artifactId: "runtime-saveable"
    version: "1.7.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.runtime"
    artifactId: "runtime-saveable-android"
    version: "1.7.0"
  }
  digests {
    sha256: "-ggP\3726\371\360$7\005\035=^\345\341`\266q\302\232\251l1\njK\215\307\251\326\226"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui"
    version: "1.6.6"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-android"
    version: "1.6.6"
  }
  digests {
    sha256: "\031s\271\241#\212CnURh\352\273\325v\357\213r\203\241j\r\250x\r_\254|e\267[\""
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.autofill"
    artifactId: "autofill"
    version: "1.0.0"
  }
  digests {
    sha256: "\311F\217V\340P\006\352\025\032BlT\225|\320y\233\213\203\245y\322\204m\322 a\363>^\315"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-geometry"
    version: "1.6.6"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-geometry-android"
    version: "1.6.6"
  }
  digests {
    sha256: ">Z\201w\332w\377\201\373\370w\313\305S\352B9\226\345\025\365\026\303\327\233\263\254Kx\302\v\210"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-util"
    version: "1.6.6"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-util-android"
    version: "1.6.6"
  }
  digests {
    sha256: "\371\204\031\277\v\331\316\362k\335\027\332\324\016\203\b\351<\352\257B\3671\210\374\215\'7Dh|\204"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-graphics"
    version: "1.6.6"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-graphics-android"
    version: "1.6.6"
  }
  digests {
    sha256: "\024\261RX\031At\243<\004\234\252P\326\367\344\352\207\354\2529\241\256T\021\250\033\331W?m3"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-unit"
    version: "1.6.6"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-unit-android"
    version: "1.6.6"
  }
  digests {
    sha256: "\005\321o_uQ\021\260\263U\274\006\243\352\245\374%\355\035\322s\360%\352\322\036w\"\322\037m\203"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-text"
    version: "1.6.6"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-text-android"
    version: "1.6.6"
  }
  digests {
    sha256: "\302:_\274\030\026\234\311{\t\300\023r\000\256\034E\317\206(m\000-)\326\327mdu\240j\002"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.emoji2"
    artifactId: "emoji2"
    version: "1.3.0"
  }
  digests {
    sha256: "+\3628\030\262:\231m\332\241\265\375[\263!)\332\377k\273-\316\025\026n/\314\335 \020\261\245"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-tooling-preview"
    version: "1.6.6"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-tooling-preview-android"
    version: "1.6.6"
  }
  digests {
    sha256: "\352\2320\2109\370~\030E\257Fb\357<\316/\246\370\313+\271\f\nA]fJl\244\263\271Q"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.customview"
    artifactId: "customview-poolingcontainer"
    version: "1.0.0"
  }
  digests {
    sha256: "5\204\020/\304\233\363\231\305n;{\344\277\341 \000\304a\0222\f\330\317\205\314\n\217\223\363\347R"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.foundation"
    artifactId: "foundation"
    version: "1.6.6"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.foundation"
    artifactId: "foundation-android"
    version: "1.6.6"
  }
  digests {
    sha256: "~?\025\267\nz\301c\225%\231P\352-\344#\030\026\320\376\317\343\245P\204i,\354\252\214,$"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.animation"
    artifactId: "animation"
    version: "1.6.6"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.animation"
    artifactId: "animation-android"
    version: "1.6.6"
  }
  digests {
    sha256: "\255<\"&\357\263\216}\021b\332)\321J\006\265\240c\rN\313\306\336u\271\243\331\027B\212\253\314"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.animation"
    artifactId: "animation-core"
    version: "1.6.6"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.animation"
    artifactId: "animation-core-android"
    version: "1.6.6"
  }
  digests {
    sha256: "\373\207F\\V\315W\300\002f\005\367\267\346\356\006!\314t\024\320\026x\355\344\376V\262J.\360\221"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.foundation"
    artifactId: "foundation-layout"
    version: "1.6.6"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.foundation"
    artifactId: "foundation-layout-android"
    version: "1.6.6"
  }
  digests {
    sha256: "\341K\033\316\270\351z\234+\326\376s=\r\242^Q\274\365\210\331a\231\334\361\320m6\350Q\220;"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose"
    artifactId: "compose-bom"
    version: "2024.04.01"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.material3"
    artifactId: "material3"
    version: "1.2.1"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.material3"
    artifactId: "material3-android"
    version: "1.2.1"
  }
  digests {
    sha256: "\323D\340\3270`\315\344\273\300\355d\024i\206\312\037\256\016\016\311S\255\226\230q>\335+\321\026\000"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.material"
    artifactId: "material-icons-core"
    version: "1.6.6"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.material"
    artifactId: "material-icons-core-android"
    version: "1.6.6"
  }
  digests {
    sha256: "\bO\237O\003\242\332Y;\315v\372!\310r\005C\255\377\027\322}2\242\235\257\343\322\303\251\267\224"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.material"
    artifactId: "material-ripple"
    version: "1.6.6"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.material"
    artifactId: "material-ripple-android"
    version: "1.6.6"
  }
  digests {
    sha256: "\235:\344\336\034\336\312\343\034N[\f\271\255\360\273\001\255U%\252\207\004t\034\036\275T\301\'\223\312"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.squareup.okhttp3"
    artifactId: "okhttp"
    version: "4.12.0"
  }
  digests {
    sha256: "\261\005\000\201\261K\267\243\247\345ZM>\360\033]\317\253\304S\264W:O\300\031vq\221\325\364\340"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.squareup.okio"
    artifactId: "okio"
    version: "3.6.0"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.squareup.okio"
    artifactId: "okio-jvm"
    version: "3.6.0"
  }
  digests {
    sha256: "gT?\a6\374B*\351\'\355\016PK\230\274^&\237\332\r5\000W\2237\313q=\242\204\022"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "io.coil-kt"
    artifactId: "coil-compose"
    version: "2.5.0"
  }
  digests {
    sha256: "\221\177@G\\\033 la\244\2115X3\341\021\346\375\347)\354\v\216<\\\312\271#\262\203d/"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "io.coil-kt"
    artifactId: "coil-compose-base"
    version: "2.5.0"
  }
  digests {
    sha256: "\020h\no\000\253c\006Sz&\207\"*\260\377b\260\255\203\355}\345\360)G\322\331\325\302\004\v"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.accompanist"
    artifactId: "accompanist-drawablepainter"
    version: "0.32.0"
  }
  digests {
    sha256: "h\r\'\225\017\221\272\030j\241E\"\255\001\235\255\222\307\002$\215\021\016\275\326\304\227sJ\275}\240"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "io.coil-kt"
    artifactId: "coil-base"
    version: "2.5.0"
  }
  digests {
    sha256: "\2739\237\2009\350\317\177\025\221\002\310[D(\033\342\377~\274X!@V\370`\034\244?\276\253\305"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.appcompat"
    artifactId: "appcompat-resources"
    version: "1.6.1"
  }
  digests {
    sha256: "\333\221]\277I5xc\336\026i\377\237\335\216\220\b\326_\343W\257l\316\232\3460C\255_f\027"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.vectordrawable"
    artifactId: "vectordrawable"
    version: "1.1.0"
  }
  digests {
    sha256: "F\375c:\300\033I\267\374\253\302c\277\t\214Z\213\236\232iwM#N\334\312\004\373\002\337\216&"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.vectordrawable"
    artifactId: "vectordrawable-animated"
    version: "1.1.0"
  }
  digests {
    sha256: "v\332,P#q\331\303\200T\337^+$\215\000\332\207\200\236\320X\3636>\256\207\316^$\003\370"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.exifinterface"
    artifactId: "exifinterface"
    version: "1.3.6"
  }
  digests {
    sha256: "\030\004\020^\236\005\375\330\367`A;\255]\344\230\303\201\2522\237O\235\224\310Q\274\211\032\306T\306"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "io.coil-kt"
    artifactId: "coil"
    version: "2.5.0"
  }
  digests {
    sha256: "\304\243\306^\301\275T0~V\356-D\177[kH\214V\361\373\373Z\020\316\301\320\3443\f\3413"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.accompanist"
    artifactId: "accompanist-permissions"
    version: "0.32.0"
  }
  digests {
    sha256: "\264\324\r\243\371\331\267\221h\271\317\r\252\a2\000\3509\356|\215x\213\351\233<\370u\356\255\302D"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "io.github.aakira"
    artifactId: "napier"
    version: "1.4.1"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "io.github.aakira"
    artifactId: "napier-android"
    version: "1.4.1"
  }
  digests {
    sha256: "&\234\307\264:.K]7\337)8\322u-)F\276\251\271\217\373\200\3540\257\025\003\200E\037L"
  }
  repo_index {
    value: 1
  }
}
library_dependencies {
  library_dep_index: 1
  library_dep_index: 2
  library_dep_index: 3
  library_dep_index: 4
}
library_dependencies {
  library_index: 2
  library_dep_index: 0
  library_dep_index: 3
}
library_dependencies {
  library_index: 3
  library_dep_index: 0
}
library_dependencies {
  library_index: 4
  library_dep_index: 0
}
library_dependencies {
  library_index: 5
  library_dep_index: 6
  library_dep_index: 8
  library_dep_index: 0
  library_dep_index: 8
}
library_dependencies {
  library_index: 6
  library_dep_index: 7
}
library_dependencies {
  library_index: 7
  library_dep_index: 0
}
library_dependencies {
  library_index: 8
  library_dep_index: 6
  library_dep_index: 9
  library_dep_index: 10
  library_dep_index: 13
  library_dep_index: 15
  library_dep_index: 16
  library_dep_index: 30
  library_dep_index: 40
  library_dep_index: 0
  library_dep_index: 5
}
library_dependencies {
  library_index: 9
  library_dep_index: 0
}
library_dependencies {
  library_index: 10
  library_dep_index: 11
}
library_dependencies {
  library_index: 11
  library_dep_index: 6
  library_dep_index: 0
  library_dep_index: 12
  library_dep_index: 12
}
library_dependencies {
  library_index: 12
  library_dep_index: 10
  library_dep_index: 10
}
library_dependencies {
  library_index: 13
  library_dep_index: 6
  library_dep_index: 14
}
library_dependencies {
  library_index: 15
  library_dep_index: 6
}
library_dependencies {
  library_index: 16
  library_dep_index: 17
}
library_dependencies {
  library_index: 17
  library_dep_index: 6
  library_dep_index: 18
  library_dep_index: 19
  library_dep_index: 20
  library_dep_index: 39
  library_dep_index: 0
  library_dep_index: 25
  library_dep_index: 20
  library_dep_index: 26
  library_dep_index: 31
  library_dep_index: 33
  library_dep_index: 35
  library_dep_index: 36
  library_dep_index: 27
  library_dep_index: 28
}
library_dependencies {
  library_index: 18
  library_dep_index: 6
}
library_dependencies {
  library_index: 19
  library_dep_index: 6
  library_dep_index: 18
}
library_dependencies {
  library_index: 20
  library_dep_index: 21
}
library_dependencies {
  library_index: 21
  library_dep_index: 6
  library_dep_index: 0
  library_dep_index: 22
  library_dep_index: 26
  library_dep_index: 16
  library_dep_index: 31
  library_dep_index: 33
  library_dep_index: 35
  library_dep_index: 36
  library_dep_index: 27
  library_dep_index: 28
}
library_dependencies {
  library_index: 22
  library_dep_index: 23
}
library_dependencies {
  library_index: 23
  library_dep_index: 1
  library_dep_index: 24
  library_dep_index: 4
  library_dep_index: 2
}
library_dependencies {
  library_index: 24
  library_dep_index: 25
  library_dep_index: 23
  library_dep_index: 22
}
library_dependencies {
  library_index: 25
  library_dep_index: 22
  library_dep_index: 24
  library_dep_index: 2
}
library_dependencies {
  library_index: 26
  library_dep_index: 6
  library_dep_index: 20
  library_dep_index: 20
  library_dep_index: 27
  library_dep_index: 16
  library_dep_index: 31
  library_dep_index: 33
  library_dep_index: 35
  library_dep_index: 36
  library_dep_index: 28
}
library_dependencies {
  library_index: 27
  library_dep_index: 18
  library_dep_index: 19
  library_dep_index: 20
  library_dep_index: 0
  library_dep_index: 20
  library_dep_index: 26
  library_dep_index: 28
  library_dep_index: 16
  library_dep_index: 31
  library_dep_index: 33
  library_dep_index: 35
  library_dep_index: 36
}
library_dependencies {
  library_index: 28
  library_dep_index: 6
  library_dep_index: 16
  library_dep_index: 29
  library_dep_index: 0
  library_dep_index: 20
  library_dep_index: 26
  library_dep_index: 27
  library_dep_index: 16
  library_dep_index: 31
  library_dep_index: 33
  library_dep_index: 35
  library_dep_index: 36
}
library_dependencies {
  library_index: 29
  library_dep_index: 6
  library_dep_index: 30
}
library_dependencies {
  library_index: 30
  library_dep_index: 6
}
library_dependencies {
  library_index: 31
  library_dep_index: 32
}
library_dependencies {
  library_index: 32
  library_dep_index: 6
  library_dep_index: 16
  library_dep_index: 0
  library_dep_index: 25
  library_dep_index: 20
  library_dep_index: 16
  library_dep_index: 33
  library_dep_index: 35
  library_dep_index: 36
  library_dep_index: 26
  library_dep_index: 27
  library_dep_index: 28
}
library_dependencies {
  library_index: 33
  library_dep_index: 34
}
library_dependencies {
  library_index: 34
  library_dep_index: 6
  library_dep_index: 0
  library_dep_index: 25
  library_dep_index: 22
  library_dep_index: 20
  library_dep_index: 26
  library_dep_index: 16
  library_dep_index: 31
  library_dep_index: 35
  library_dep_index: 36
  library_dep_index: 27
  library_dep_index: 28
}
library_dependencies {
  library_index: 35
  library_dep_index: 33
  library_dep_index: 0
  library_dep_index: 25
  library_dep_index: 20
  library_dep_index: 16
  library_dep_index: 31
  library_dep_index: 33
  library_dep_index: 36
  library_dep_index: 26
  library_dep_index: 27
  library_dep_index: 28
}
library_dependencies {
  library_index: 36
  library_dep_index: 6
  library_dep_index: 5
  library_dep_index: 27
  library_dep_index: 33
  library_dep_index: 37
  library_dep_index: 0
  library_dep_index: 25
  library_dep_index: 20
  library_dep_index: 26
  library_dep_index: 27
  library_dep_index: 16
  library_dep_index: 31
  library_dep_index: 33
  library_dep_index: 35
  library_dep_index: 28
}
library_dependencies {
  library_index: 37
  library_dep_index: 6
  library_dep_index: 18
  library_dep_index: 20
  library_dep_index: 0
  library_dep_index: 38
}
library_dependencies {
  library_index: 38
  library_dep_index: 37
  library_dep_index: 0
  library_dep_index: 37
}
library_dependencies {
  library_index: 39
  library_dep_index: 6
  library_dep_index: 13
  library_dep_index: 29
  library_dep_index: 14
}
library_dependencies {
  library_index: 40
  library_dep_index: 6
  library_dep_index: 10
}
library_dependencies {
  library_index: 41
  library_dep_index: 42
  library_dep_index: 45
  library_dep_index: 47
  library_dep_index: 49
  library_dep_index: 5
  library_dep_index: 20
  library_dep_index: 16
  library_dep_index: 33
  library_dep_index: 37
  library_dep_index: 0
  library_dep_index: 22
  library_dep_index: 42
  library_dep_index: 0
  library_dep_index: 43
}
library_dependencies {
  library_index: 42
  library_dep_index: 43
  library_dep_index: 31
  library_dep_index: 35
  library_dep_index: 38
  library_dep_index: 43
  library_dep_index: 41
}
library_dependencies {
  library_index: 43
  library_dep_index: 6
  library_dep_index: 5
  library_dep_index: 44
  library_dep_index: 20
  library_dep_index: 16
  library_dep_index: 33
  library_dep_index: 36
  library_dep_index: 39
  library_dep_index: 37
  library_dep_index: 30
  library_dep_index: 0
  library_dep_index: 22
  library_dep_index: 41
  library_dep_index: 42
  library_dep_index: 0
}
library_dependencies {
  library_index: 44
  library_dep_index: 0
  library_dep_index: 0
}
library_dependencies {
  library_index: 45
  library_dep_index: 46
}
library_dependencies {
  library_index: 46
  library_dep_index: 9
  library_dep_index: 10
  library_dep_index: 0
  library_dep_index: 4
  library_dep_index: 25
  library_dep_index: 22
  library_dep_index: 47
}
library_dependencies {
  library_index: 47
  library_dep_index: 48
}
library_dependencies {
  library_index: 48
  library_dep_index: 6
  library_dep_index: 45
  library_dep_index: 0
  library_dep_index: 4
  library_dep_index: 45
}
library_dependencies {
  library_index: 49
  library_dep_index: 50
}
library_dependencies {
  library_index: 50
  library_dep_index: 42
  library_dep_index: 6
  library_dep_index: 51
  library_dep_index: 10
  library_dep_index: 10
  library_dep_index: 45
  library_dep_index: 47
  library_dep_index: 52
  library_dep_index: 56
  library_dep_index: 60
  library_dep_index: 58
  library_dep_index: 54
  library_dep_index: 8
  library_dep_index: 65
  library_dep_index: 62
  library_dep_index: 16
  library_dep_index: 33
  library_dep_index: 39
  library_dep_index: 38
  library_dep_index: 0
  library_dep_index: 4
  library_dep_index: 25
  library_dep_index: 22
  library_dep_index: 52
  library_dep_index: 56
  library_dep_index: 60
  library_dep_index: 63
  library_dep_index: 58
  library_dep_index: 54
  library_dep_index: 66
}
library_dependencies {
  library_index: 51
  library_dep_index: 8
}
library_dependencies {
  library_index: 52
  library_dep_index: 53
}
library_dependencies {
  library_index: 53
  library_dep_index: 6
  library_dep_index: 45
  library_dep_index: 54
  library_dep_index: 0
  library_dep_index: 4
  library_dep_index: 49
  library_dep_index: 56
  library_dep_index: 60
  library_dep_index: 63
  library_dep_index: 58
  library_dep_index: 54
}
library_dependencies {
  library_index: 54
  library_dep_index: 55
}
library_dependencies {
  library_index: 55
  library_dep_index: 0
  library_dep_index: 4
  library_dep_index: 49
  library_dep_index: 52
  library_dep_index: 56
  library_dep_index: 60
  library_dep_index: 63
  library_dep_index: 58
}
library_dependencies {
  library_index: 56
  library_dep_index: 57
}
library_dependencies {
  library_index: 57
  library_dep_index: 6
  library_dep_index: 10
  library_dep_index: 45
  library_dep_index: 58
  library_dep_index: 54
  library_dep_index: 4
  library_dep_index: 49
  library_dep_index: 52
  library_dep_index: 60
  library_dep_index: 63
  library_dep_index: 58
  library_dep_index: 54
}
library_dependencies {
  library_index: 58
  library_dep_index: 59
}
library_dependencies {
  library_index: 59
  library_dep_index: 6
  library_dep_index: 12
  library_dep_index: 45
  library_dep_index: 52
  library_dep_index: 54
  library_dep_index: 0
  library_dep_index: 4
  library_dep_index: 49
  library_dep_index: 52
  library_dep_index: 56
  library_dep_index: 60
  library_dep_index: 63
  library_dep_index: 54
}
library_dependencies {
  library_index: 60
  library_dep_index: 61
}
library_dependencies {
  library_index: 61
  library_dep_index: 6
  library_dep_index: 10
  library_dep_index: 45
  library_dep_index: 47
  library_dep_index: 56
  library_dep_index: 58
  library_dep_index: 54
  library_dep_index: 8
  library_dep_index: 62
  library_dep_index: 0
  library_dep_index: 4
  library_dep_index: 22
  library_dep_index: 49
  library_dep_index: 52
  library_dep_index: 56
  library_dep_index: 63
  library_dep_index: 58
  library_dep_index: 54
}
library_dependencies {
  library_index: 62
  library_dep_index: 6
  library_dep_index: 10
  library_dep_index: 8
  library_dep_index: 28
  library_dep_index: 29
}
library_dependencies {
  library_index: 63
  library_dep_index: 64
}
library_dependencies {
  library_index: 64
  library_dep_index: 6
  library_dep_index: 45
  library_dep_index: 4
  library_dep_index: 49
  library_dep_index: 52
  library_dep_index: 56
  library_dep_index: 60
  library_dep_index: 58
  library_dep_index: 54
}
library_dependencies {
  library_index: 65
  library_dep_index: 5
  library_dep_index: 0
}
library_dependencies {
  library_index: 66
  library_dep_index: 67
}
library_dependencies {
  library_index: 67
  library_dep_index: 6
  library_dep_index: 10
  library_dep_index: 68
  library_dep_index: 72
  library_dep_index: 45
  library_dep_index: 49
  library_dep_index: 60
  library_dep_index: 54
  library_dep_index: 8
  library_dep_index: 62
  library_dep_index: 4
  library_dep_index: 72
}
library_dependencies {
  library_index: 68
  library_dep_index: 69
}
library_dependencies {
  library_index: 69
  library_dep_index: 6
  library_dep_index: 70
  library_dep_index: 72
  library_dep_index: 45
  library_dep_index: 49
  library_dep_index: 52
  library_dep_index: 54
  library_dep_index: 4
  library_dep_index: 70
}
library_dependencies {
  library_index: 70
  library_dep_index: 71
}
library_dependencies {
  library_index: 71
  library_dep_index: 6
  library_dep_index: 10
  library_dep_index: 45
  library_dep_index: 49
  library_dep_index: 58
  library_dep_index: 54
  library_dep_index: 0
  library_dep_index: 4
  library_dep_index: 22
  library_dep_index: 68
}
library_dependencies {
  library_index: 72
  library_dep_index: 73
}
library_dependencies {
  library_index: 73
  library_dep_index: 6
  library_dep_index: 70
  library_dep_index: 45
  library_dep_index: 49
  library_dep_index: 54
  library_dep_index: 8
  library_dep_index: 4
  library_dep_index: 66
}
library_dependencies {
  library_index: 74
  library_dep_index: 75
  library_dep_index: 45
  library_dep_index: 47
  library_dep_index: 49
  library_dep_index: 56
  library_dep_index: 63
  library_dep_index: 66
  library_dep_index: 46
  library_dep_index: 48
  library_dep_index: 76
  library_dep_index: 50
  library_dep_index: 57
  library_dep_index: 64
  library_dep_index: 67
  library_dep_index: 70
  library_dep_index: 72
  library_dep_index: 77
  library_dep_index: 79
  library_dep_index: 60
  library_dep_index: 54
  library_dep_index: 52
  library_dep_index: 58
  library_dep_index: 68
  library_dep_index: 71
  library_dep_index: 73
  library_dep_index: 78
  library_dep_index: 80
  library_dep_index: 61
  library_dep_index: 55
  library_dep_index: 53
  library_dep_index: 59
  library_dep_index: 69
}
library_dependencies {
  library_index: 75
  library_dep_index: 76
}
library_dependencies {
  library_index: 76
  library_dep_index: 41
  library_dep_index: 6
  library_dep_index: 9
  library_dep_index: 10
  library_dep_index: 70
  library_dep_index: 66
  library_dep_index: 72
  library_dep_index: 77
  library_dep_index: 79
  library_dep_index: 45
  library_dep_index: 56
  library_dep_index: 60
  library_dep_index: 54
  library_dep_index: 26
  library_dep_index: 16
  library_dep_index: 33
  library_dep_index: 38
  library_dep_index: 4
}
library_dependencies {
  library_index: 77
  library_dep_index: 78
}
library_dependencies {
  library_index: 78
  library_dep_index: 49
  library_dep_index: 0
  library_dep_index: 4
  library_dep_index: 79
}
library_dependencies {
  library_index: 79
  library_dep_index: 80
}
library_dependencies {
  library_index: 80
  library_dep_index: 68
  library_dep_index: 66
  library_dep_index: 45
  library_dep_index: 54
  library_dep_index: 4
  library_dep_index: 77
}
library_dependencies {
  library_index: 81
  library_dep_index: 82
  library_dep_index: 2
}
library_dependencies {
  library_index: 82
  library_dep_index: 83
}
library_dependencies {
  library_index: 83
  library_dep_index: 2
  library_dep_index: 4
}
library_dependencies {
  library_index: 84
  library_dep_index: 85
  library_dep_index: 92
  library_dep_index: 2
}
library_dependencies {
  library_index: 85
  library_dep_index: 5
  library_dep_index: 86
  library_dep_index: 87
  library_dep_index: 66
  library_dep_index: 2
}
library_dependencies {
  library_index: 86
  library_dep_index: 49
  library_dep_index: 25
  library_dep_index: 2
}
library_dependencies {
  library_index: 87
  library_dep_index: 6
  library_dep_index: 88
  library_dep_index: 10
  library_dep_index: 5
  library_dep_index: 91
  library_dep_index: 39
  library_dep_index: 16
  library_dep_index: 25
  library_dep_index: 0
  library_dep_index: 81
  library_dep_index: 82
}
library_dependencies {
  library_index: 88
  library_dep_index: 6
  library_dep_index: 10
  library_dep_index: 8
  library_dep_index: 89
  library_dep_index: 90
}
library_dependencies {
  library_index: 89
  library_dep_index: 6
  library_dep_index: 8
  library_dep_index: 10
}
library_dependencies {
  library_index: 90
  library_dep_index: 89
  library_dep_index: 15
  library_dep_index: 10
}
library_dependencies {
  library_index: 91
  library_dep_index: 6
}
library_dependencies {
  library_index: 92
  library_dep_index: 87
  library_dep_index: 2
}
library_dependencies {
  library_index: 93
  library_dep_index: 41
  library_dep_index: 66
  library_dep_index: 25
  library_dep_index: 94
  library_dep_index: 2
}
library_dependencies {
  library_index: 94
  library_dep_index: 95
}
library_dependencies {
  library_index: 95
  library_dep_index: 0
  library_dep_index: 4
}
module_dependencies {
  module_name: "base"
  dependency_index: 0
  dependency_index: 5
  dependency_index: 31
  dependency_index: 41
  dependency_index: 74
  dependency_index: 49
  dependency_index: 56
  dependency_index: 63
  dependency_index: 75
  dependency_index: 81
  dependency_index: 84
  dependency_index: 93
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
