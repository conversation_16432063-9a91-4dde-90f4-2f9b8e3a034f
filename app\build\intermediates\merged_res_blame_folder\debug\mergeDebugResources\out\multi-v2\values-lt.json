{"logs": [{"outputFile": "com.example.myfirstapp-mergeDebugResources-47:/values-lt/values-lt.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f98ee3d67031989130b5c72a257577d6\\transformed\\core-1.15.0\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,263,362,465,576,686,806", "endColumns": "97,109,98,102,110,109,119,100", "endOffsets": "148,258,357,460,571,681,801,902"}, "to": {"startLines": "2,3,4,5,6,7,8,79", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,203,313,412,515,626,736,8274", "endColumns": "97,109,98,102,110,109,119,100", "endOffsets": "198,308,407,510,621,731,851,8370"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\3bdae484d8629b91b71d2a8e711d605e\\transformed\\material3-release\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,174,294,411,529,630,724,835,967,1083,1227,1311,1410,1506,1605,1730,1848,1952,2091,2226,2365,2561,2691,2809,2935,3062,3159,3260,3382,3511,3609,3712,3819,3957,4105,4214,4318,4402,4498,4594,4682,4772,4883,4963,5050,5150,5259,5355,5454,5542,5653,5749,5849,5987,6071,6174", "endColumns": "118,119,116,117,100,93,110,131,115,143,83,98,95,98,124,117,103,138,134,138,195,129,117,125,126,96,100,121,128,97,102,106,137,147,108,103,83,95,95,87,89,110,79,86,99,108,95,98,87,110,95,99,137,83,102,96", "endOffsets": "169,289,406,524,625,719,830,962,1078,1222,1306,1405,1501,1600,1725,1843,1947,2086,2221,2360,2556,2686,2804,2930,3057,3154,3255,3377,3506,3604,3707,3814,3952,4100,4209,4313,4397,4493,4589,4677,4767,4878,4958,5045,5145,5254,5350,5449,5537,5648,5744,5844,5982,6066,6169,6266"}, "to": {"startLines": "16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1499,1618,1738,1855,1973,2074,2168,2279,2411,2527,2671,2755,2854,2950,3049,3174,3292,3396,3535,3670,3809,4005,4135,4253,4379,4506,4603,4704,4826,4955,5053,5156,5263,5401,5549,5658,5762,5846,5942,6038,6126,6216,6327,6407,6494,6594,6703,6799,6898,6986,7097,7193,7293,7431,7515,7618", "endColumns": "118,119,116,117,100,93,110,131,115,143,83,98,95,98,124,117,103,138,134,138,195,129,117,125,126,96,100,121,128,97,102,106,137,147,108,103,83,95,95,87,89,110,79,86,99,108,95,98,87,110,95,99,137,83,102,96", "endOffsets": "1613,1733,1850,1968,2069,2163,2274,2406,2522,2666,2750,2849,2945,3044,3169,3287,3391,3530,3665,3804,4000,4130,4248,4374,4501,4598,4699,4821,4950,5048,5151,5258,5396,5544,5653,5757,5841,5937,6033,6121,6211,6322,6402,6489,6589,6698,6794,6893,6981,7092,7188,7288,7426,7510,7613,7710"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a575f2fdfb28ee7d8146806f00191903\\transformed\\foundation-release\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,143", "endColumns": "87,87", "endOffsets": "138,226"}, "to": {"startLines": "83,84", "startColumns": "4,4", "startOffsets": "8660,8748", "endColumns": "87,87", "endOffsets": "8743,8831"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\d223efed7c9b71e5170a4e85d2ac6918\\transformed\\ui-release\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,282,380,485,580,657,748,835,919,989,1058,1144,1232,1307,1387,1470", "endColumns": "92,83,97,104,94,76,90,86,83,69,68,85,87,74,79,82,121", "endOffsets": "193,277,375,480,575,652,743,830,914,984,1053,1139,1227,1302,1382,1465,1587"}, "to": {"startLines": "9,10,11,12,13,14,15,72,73,74,75,76,77,78,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "856,949,1033,1131,1236,1331,1408,7715,7802,7886,7956,8025,8111,8199,8375,8455,8538", "endColumns": "92,83,97,104,94,76,90,86,83,69,68,85,87,74,79,82,121", "endOffsets": "944,1028,1126,1231,1326,1403,1494,7797,7881,7951,8020,8106,8194,8269,8450,8533,8655"}}]}]}