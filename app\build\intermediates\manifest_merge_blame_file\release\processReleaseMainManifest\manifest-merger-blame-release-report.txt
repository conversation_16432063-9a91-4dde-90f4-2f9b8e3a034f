1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.myfirstapp"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <!-- Required Permissions -->
12    <uses-permission android:name="android.permission.CAMERA" />
12-->C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:6:5-64
12-->C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:6:22-62
13    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
13-->C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:7:5-79
13-->C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:7:22-77
14    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
14-->C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:8:5-80
14-->C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:8:22-78
15    <uses-permission android:name="android.permission.INTERNET" />
15-->C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:9:5-67
15-->C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:9:22-64
16
17    <permission
17-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f98ee3d67031989130b5c72a257577d6\transformed\core-1.15.0\AndroidManifest.xml:22:5-24:47
18        android:name="com.example.myfirstapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
18-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f98ee3d67031989130b5c72a257577d6\transformed\core-1.15.0\AndroidManifest.xml:23:9-81
19        android:protectionLevel="signature" />
19-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f98ee3d67031989130b5c72a257577d6\transformed\core-1.15.0\AndroidManifest.xml:24:9-44
20
21    <uses-permission android:name="com.example.myfirstapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
21-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f98ee3d67031989130b5c72a257577d6\transformed\core-1.15.0\AndroidManifest.xml:26:5-97
21-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f98ee3d67031989130b5c72a257577d6\transformed\core-1.15.0\AndroidManifest.xml:26:22-94
22
23    <application
23-->C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:11:5-43:19
24        android:allowBackup="true"
24-->C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:12:9-35
25        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
25-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f98ee3d67031989130b5c72a257577d6\transformed\core-1.15.0\AndroidManifest.xml:28:18-86
26        android:dataExtractionRules="@xml/data_extraction_rules"
26-->C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:13:9-65
27        android:extractNativeLibs="false"
28        android:fullBackupContent="@xml/backup_rules"
28-->C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:14:9-54
29        android:icon="@mipmap/ic_launcher"
29-->C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:15:9-43
30        android:label="@string/app_name"
30-->C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:16:9-41
31        android:roundIcon="@mipmap/ic_launcher_round"
31-->C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:17:9-54
32        android:supportsRtl="true"
32-->C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:18:9-35
33        android:theme="@style/Theme.MyFirstApp" >
33-->C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:19:9-48
34
35        <!-- Camera File Provider -->
36        <provider
37            android:name="androidx.core.content.FileProvider"
37-->C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:24:13-62
38            android:authorities="com.example.myfirstapp.provider"
38-->C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:25:13-60
39            android:exported="false"
39-->C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:26:13-37
40            android:grantUriPermissions="true" >
40-->C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:27:13-47
41            <meta-data
41-->C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:28:13-30:54
42                android:name="android.support.FILE_PROVIDER_PATHS"
42-->C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:29:17-67
43                android:resource="@xml/file_paths" />
43-->C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:30:17-51
44        </provider>
45
46        <activity
46-->C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:33:9-42:20
47            android:name="com.example.myfirstapp.MainActivity"
47-->C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:34:13-41
48            android:exported="true"
48-->C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:35:13-36
49            android:label="@string/app_name"
49-->C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:36:13-45
50            android:theme="@style/Theme.MyFirstApp" >
50-->C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:37:13-52
51            <intent-filter>
51-->C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:38:13-41:29
52                <action android:name="android.intent.action.MAIN" />
52-->C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:39:17-68
52-->C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:39:25-66
53
54                <category android:name="android.intent.category.LAUNCHER" />
54-->C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:40:17-76
54-->C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:40:27-74
55            </intent-filter>
56        </activity>
57
58        <provider
58-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c99f34d6b8482bfcbb773832aa0cf7d\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
59            android:name="androidx.startup.InitializationProvider"
59-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c99f34d6b8482bfcbb773832aa0cf7d\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
60            android:authorities="com.example.myfirstapp.androidx-startup"
60-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c99f34d6b8482bfcbb773832aa0cf7d\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
61            android:exported="false" >
61-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c99f34d6b8482bfcbb773832aa0cf7d\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
62            <meta-data
62-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c99f34d6b8482bfcbb773832aa0cf7d\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
63                android:name="androidx.emoji2.text.EmojiCompatInitializer"
63-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c99f34d6b8482bfcbb773832aa0cf7d\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
64                android:value="androidx.startup" />
64-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c99f34d6b8482bfcbb773832aa0cf7d\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
65            <meta-data
65-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f1e6ac602e46892c730ac0637c95abd7\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:29:13-31:52
66                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
66-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f1e6ac602e46892c730ac0637c95abd7\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:30:17-78
67                android:value="androidx.startup" />
67-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f1e6ac602e46892c730ac0637c95abd7\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:31:17-49
68            <meta-data
68-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\453499689d447916c8e1e9c7fa388b45\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
69                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
69-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\453499689d447916c8e1e9c7fa388b45\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
70                android:value="androidx.startup" />
70-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\453499689d447916c8e1e9c7fa388b45\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
71        </provider>
72
73        <receiver
73-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\453499689d447916c8e1e9c7fa388b45\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
74            android:name="androidx.profileinstaller.ProfileInstallReceiver"
74-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\453499689d447916c8e1e9c7fa388b45\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
75            android:directBootAware="false"
75-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\453499689d447916c8e1e9c7fa388b45\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
76            android:enabled="true"
76-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\453499689d447916c8e1e9c7fa388b45\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
77            android:exported="true"
77-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\453499689d447916c8e1e9c7fa388b45\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
78            android:permission="android.permission.DUMP" >
78-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\453499689d447916c8e1e9c7fa388b45\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
79            <intent-filter>
79-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\453499689d447916c8e1e9c7fa388b45\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
80                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
80-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\453499689d447916c8e1e9c7fa388b45\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
80-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\453499689d447916c8e1e9c7fa388b45\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
81            </intent-filter>
82            <intent-filter>
82-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\453499689d447916c8e1e9c7fa388b45\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
83                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
83-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\453499689d447916c8e1e9c7fa388b45\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
83-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\453499689d447916c8e1e9c7fa388b45\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
84            </intent-filter>
85            <intent-filter>
85-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\453499689d447916c8e1e9c7fa388b45\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
86                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
86-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\453499689d447916c8e1e9c7fa388b45\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
86-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\453499689d447916c8e1e9c7fa388b45\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
87            </intent-filter>
88            <intent-filter>
88-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\453499689d447916c8e1e9c7fa388b45\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
89                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
89-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\453499689d447916c8e1e9c7fa388b45\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
89-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\453499689d447916c8e1e9c7fa388b45\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
90            </intent-filter>
91        </receiver>
92    </application>
93
94</manifest>
