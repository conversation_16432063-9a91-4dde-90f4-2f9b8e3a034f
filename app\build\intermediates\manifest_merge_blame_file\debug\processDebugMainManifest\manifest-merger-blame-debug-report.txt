1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.myfirstapp"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <!-- Required Permissions -->
12    <uses-permission android:name="android.permission.CAMERA" />
12-->C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:6:5-64
12-->C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:6:22-62
13    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
13-->C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:7:5-79
13-->C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:7:22-77
14    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
14-->C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:8:5-80
14-->C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:8:22-78
15    <uses-permission android:name="android.permission.INTERNET" />
15-->C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:9:5-67
15-->C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:9:22-64
16
17    <permission
17-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f98ee3d67031989130b5c72a257577d6\transformed\core-1.15.0\AndroidManifest.xml:22:5-24:47
18        android:name="com.example.myfirstapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
18-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f98ee3d67031989130b5c72a257577d6\transformed\core-1.15.0\AndroidManifest.xml:23:9-81
19        android:protectionLevel="signature" />
19-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f98ee3d67031989130b5c72a257577d6\transformed\core-1.15.0\AndroidManifest.xml:24:9-44
20
21    <uses-permission android:name="com.example.myfirstapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
21-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f98ee3d67031989130b5c72a257577d6\transformed\core-1.15.0\AndroidManifest.xml:26:5-97
21-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f98ee3d67031989130b5c72a257577d6\transformed\core-1.15.0\AndroidManifest.xml:26:22-94
22
23    <application
23-->C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:11:5-43:19
24        android:allowBackup="true"
24-->C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:12:9-35
25        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
25-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f98ee3d67031989130b5c72a257577d6\transformed\core-1.15.0\AndroidManifest.xml:28:18-86
26        android:dataExtractionRules="@xml/data_extraction_rules"
26-->C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:13:9-65
27        android:debuggable="true"
28        android:extractNativeLibs="false"
29        android:fullBackupContent="@xml/backup_rules"
29-->C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:14:9-54
30        android:icon="@mipmap/ic_launcher"
30-->C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:15:9-43
31        android:label="@string/app_name"
31-->C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:16:9-41
32        android:roundIcon="@mipmap/ic_launcher_round"
32-->C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:17:9-54
33        android:supportsRtl="true"
33-->C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:18:9-35
34        android:testOnly="true"
35        android:theme="@style/Theme.MyFirstApp" >
35-->C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:19:9-48
36
37        <!-- Camera File Provider -->
38        <provider
39            android:name="androidx.core.content.FileProvider"
39-->C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:24:13-62
40            android:authorities="com.example.myfirstapp.provider"
40-->C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:25:13-60
41            android:exported="false"
41-->C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:26:13-37
42            android:grantUriPermissions="true" >
42-->C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:27:13-47
43            <meta-data
43-->C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:28:13-30:54
44                android:name="android.support.FILE_PROVIDER_PATHS"
44-->C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:29:17-67
45                android:resource="@xml/file_paths" />
45-->C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:30:17-51
46        </provider>
47
48        <activity
48-->C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:33:9-42:20
49            android:name="com.example.myfirstapp.MainActivity"
49-->C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:34:13-41
50            android:exported="true"
50-->C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:35:13-36
51            android:label="@string/app_name"
51-->C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:36:13-45
52            android:theme="@style/Theme.MyFirstApp" >
52-->C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:37:13-52
53            <intent-filter>
53-->C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:38:13-41:29
54                <action android:name="android.intent.action.MAIN" />
54-->C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:39:17-68
54-->C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:39:25-66
55
56                <category android:name="android.intent.category.LAUNCHER" />
56-->C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:40:17-76
56-->C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:40:27-74
57            </intent-filter>
58        </activity>
59        <activity
59-->[androidx.compose.ui:ui-tooling-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d25cf0741a1dd4001a29bb4db0de5ca7\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
60            android:name="androidx.compose.ui.tooling.PreviewActivity"
60-->[androidx.compose.ui:ui-tooling-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d25cf0741a1dd4001a29bb4db0de5ca7\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
61            android:exported="true" />
61-->[androidx.compose.ui:ui-tooling-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d25cf0741a1dd4001a29bb4db0de5ca7\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
62        <activity
62-->[androidx.compose.ui:ui-test-manifest:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7122f04723363524ebfb93d2197207ef\transformed\ui-test-manifest-1.6.6\AndroidManifest.xml:23:9-25:39
63            android:name="androidx.activity.ComponentActivity"
63-->[androidx.compose.ui:ui-test-manifest:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7122f04723363524ebfb93d2197207ef\transformed\ui-test-manifest-1.6.6\AndroidManifest.xml:24:13-63
64            android:exported="true" />
64-->[androidx.compose.ui:ui-test-manifest:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7122f04723363524ebfb93d2197207ef\transformed\ui-test-manifest-1.6.6\AndroidManifest.xml:25:13-36
65
66        <provider
66-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c99f34d6b8482bfcbb773832aa0cf7d\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
67            android:name="androidx.startup.InitializationProvider"
67-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c99f34d6b8482bfcbb773832aa0cf7d\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
68            android:authorities="com.example.myfirstapp.androidx-startup"
68-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c99f34d6b8482bfcbb773832aa0cf7d\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
69            android:exported="false" >
69-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c99f34d6b8482bfcbb773832aa0cf7d\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
70            <meta-data
70-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c99f34d6b8482bfcbb773832aa0cf7d\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
71                android:name="androidx.emoji2.text.EmojiCompatInitializer"
71-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c99f34d6b8482bfcbb773832aa0cf7d\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
72                android:value="androidx.startup" />
72-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c99f34d6b8482bfcbb773832aa0cf7d\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
73            <meta-data
73-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f1e6ac602e46892c730ac0637c95abd7\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:29:13-31:52
74                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
74-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f1e6ac602e46892c730ac0637c95abd7\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:30:17-78
75                android:value="androidx.startup" />
75-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f1e6ac602e46892c730ac0637c95abd7\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:31:17-49
76            <meta-data
76-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\453499689d447916c8e1e9c7fa388b45\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
77                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
77-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\453499689d447916c8e1e9c7fa388b45\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
78                android:value="androidx.startup" />
78-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\453499689d447916c8e1e9c7fa388b45\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
79        </provider>
80
81        <receiver
81-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\453499689d447916c8e1e9c7fa388b45\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
82            android:name="androidx.profileinstaller.ProfileInstallReceiver"
82-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\453499689d447916c8e1e9c7fa388b45\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
83            android:directBootAware="false"
83-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\453499689d447916c8e1e9c7fa388b45\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
84            android:enabled="true"
84-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\453499689d447916c8e1e9c7fa388b45\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
85            android:exported="true"
85-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\453499689d447916c8e1e9c7fa388b45\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
86            android:permission="android.permission.DUMP" >
86-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\453499689d447916c8e1e9c7fa388b45\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
87            <intent-filter>
87-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\453499689d447916c8e1e9c7fa388b45\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
88                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
88-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\453499689d447916c8e1e9c7fa388b45\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
88-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\453499689d447916c8e1e9c7fa388b45\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
89            </intent-filter>
90            <intent-filter>
90-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\453499689d447916c8e1e9c7fa388b45\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
91                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
91-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\453499689d447916c8e1e9c7fa388b45\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
91-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\453499689d447916c8e1e9c7fa388b45\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
92            </intent-filter>
93            <intent-filter>
93-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\453499689d447916c8e1e9c7fa388b45\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
94                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
94-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\453499689d447916c8e1e9c7fa388b45\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
94-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\453499689d447916c8e1e9c7fa388b45\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
95            </intent-filter>
96            <intent-filter>
96-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\453499689d447916c8e1e9c7fa388b45\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
97                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
97-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\453499689d447916c8e1e9c7fa388b45\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
97-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\453499689d447916c8e1e9c7fa388b45\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
98            </intent-filter>
99        </receiver>
100    </application>
101
102</manifest>
