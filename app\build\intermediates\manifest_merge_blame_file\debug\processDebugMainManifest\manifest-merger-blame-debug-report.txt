1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.myfirstapp"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <!-- Required Permissions -->
12    <uses-permission android:name="android.permission.CAMERA" />
12-->C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:6:5-64
12-->C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:6:22-62
13    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
13-->C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:7:5-79
13-->C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:7:22-77
14    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
14-->C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:8:5-80
14-->C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:8:22-78
15    <uses-permission android:name="android.permission.INTERNET" />
15-->C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:9:5-67
15-->C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:9:22-64
16
17    <!-- Background service permissions -->
18    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
18-->C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:12:5-76
18-->C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:12:22-74
19    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_CAMERA" />
19-->C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:13:5-83
19-->C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:13:22-81
20    <uses-permission android:name="android.permission.WAKE_LOCK" />
20-->C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:14:5-67
20-->C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:14:22-65
21    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
21-->C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:15:5-76
21-->C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:15:22-74
22
23    <!-- Screen unlock detection -->
24    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
24-->C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:18:5-80
24-->C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:18:22-78
25    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
25-->C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:19:5-77
25-->C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:19:22-75
26
27    <!-- Camera features -->
28    <uses-feature
28-->C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:22:5-83
29        android:name="android.hardware.camera"
29-->C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:22:19-57
30        android:required="true" />
30-->C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:22:58-81
31    <uses-feature
31-->C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:23:5-94
32        android:name="android.hardware.camera.autofocus"
32-->C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:23:19-67
33        android:required="false" />
33-->C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:23:68-92
34
35    <permission
35-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f98ee3d67031989130b5c72a257577d6\transformed\core-1.15.0\AndroidManifest.xml:22:5-24:47
36        android:name="com.example.myfirstapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
36-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f98ee3d67031989130b5c72a257577d6\transformed\core-1.15.0\AndroidManifest.xml:23:9-81
37        android:protectionLevel="signature" />
37-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f98ee3d67031989130b5c72a257577d6\transformed\core-1.15.0\AndroidManifest.xml:24:9-44
38
39    <uses-permission android:name="com.example.myfirstapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
39-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f98ee3d67031989130b5c72a257577d6\transformed\core-1.15.0\AndroidManifest.xml:26:5-97
39-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f98ee3d67031989130b5c72a257577d6\transformed\core-1.15.0\AndroidManifest.xml:26:22-94
40
41    <application
41-->C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:25:5-76:19
42        android:allowBackup="true"
42-->C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:26:9-35
43        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
43-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f98ee3d67031989130b5c72a257577d6\transformed\core-1.15.0\AndroidManifest.xml:28:18-86
44        android:dataExtractionRules="@xml/data_extraction_rules"
44-->C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:27:9-65
45        android:debuggable="true"
46        android:extractNativeLibs="false"
47        android:fullBackupContent="@xml/backup_rules"
47-->C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:28:9-54
48        android:icon="@mipmap/ic_launcher"
48-->C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:29:9-43
49        android:label="@string/app_name"
49-->C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:30:9-41
50        android:roundIcon="@mipmap/ic_launcher_round"
50-->C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:31:9-54
51        android:supportsRtl="true"
51-->C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:32:9-35
52        android:theme="@style/Theme.MyFirstApp" >
52-->C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:33:9-48
53
54        <!-- Camera File Provider -->
55        <provider
56            android:name="androidx.core.content.FileProvider"
56-->C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:38:13-62
57            android:authorities="com.example.myfirstapp.provider"
57-->C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:39:13-60
58            android:exported="false"
58-->C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:40:13-37
59            android:grantUriPermissions="true" >
59-->C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:41:13-47
60            <meta-data
60-->C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:42:13-44:54
61                android:name="android.support.FILE_PROVIDER_PATHS"
61-->C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:43:17-67
62                android:resource="@xml/file_paths" />
62-->C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:44:17-51
63        </provider>
64
65        <activity
65-->C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:47:9-56:20
66            android:name="com.example.myfirstapp.MainActivity"
66-->C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:48:13-41
67            android:exported="true"
67-->C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:49:13-36
68            android:label="@string/app_name"
68-->C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:50:13-45
69            android:theme="@style/Theme.MyFirstApp" >
69-->C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:51:13-52
70            <intent-filter>
70-->C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:52:13-55:29
71                <action android:name="android.intent.action.MAIN" />
71-->C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:53:17-68
71-->C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:53:25-66
72
73                <category android:name="android.intent.category.LAUNCHER" />
73-->C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:54:17-76
73-->C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:54:27-74
74            </intent-filter>
75        </activity>
76
77        <!-- Background Camera Service -->
78        <service
78-->C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:59:9-63:54
79            android:name="com.example.myfirstapp.BackgroundCameraService"
79-->C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:60:13-52
80            android:enabled="true"
80-->C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:61:13-35
81            android:exported="false"
81-->C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:62:13-37
82            android:foregroundServiceType="camera" />
82-->C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:63:13-51
83
84        <!-- Screen Unlock Receiver -->
85        <receiver
85-->C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:66:9-75:20
86            android:name="com.example.myfirstapp.ScreenUnlockReceiver"
86-->C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:67:13-49
87            android:enabled="true"
87-->C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:68:13-35
88            android:exported="false" >
88-->C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:69:13-37
89            <intent-filter android:priority="1000" >
89-->C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:70:13-74:29
89-->C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:70:28-51
90                <action android:name="android.intent.action.SCREEN_ON" />
90-->C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:71:17-73
90-->C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:71:25-71
91                <action android:name="android.intent.action.USER_PRESENT" />
91-->C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:72:17-76
91-->C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:72:25-74
92                <action android:name="android.intent.action.BOOT_COMPLETED" />
92-->C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:73:17-78
92-->C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:73:25-76
93            </intent-filter>
94        </receiver>
95
96        <activity
96-->[androidx.compose.ui:ui-tooling-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d25cf0741a1dd4001a29bb4db0de5ca7\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
97            android:name="androidx.compose.ui.tooling.PreviewActivity"
97-->[androidx.compose.ui:ui-tooling-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d25cf0741a1dd4001a29bb4db0de5ca7\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
98            android:exported="true" />
98-->[androidx.compose.ui:ui-tooling-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d25cf0741a1dd4001a29bb4db0de5ca7\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
99        <activity
99-->[androidx.compose.ui:ui-test-manifest:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7122f04723363524ebfb93d2197207ef\transformed\ui-test-manifest-1.6.6\AndroidManifest.xml:23:9-25:39
100            android:name="androidx.activity.ComponentActivity"
100-->[androidx.compose.ui:ui-test-manifest:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7122f04723363524ebfb93d2197207ef\transformed\ui-test-manifest-1.6.6\AndroidManifest.xml:24:13-63
101            android:exported="true" />
101-->[androidx.compose.ui:ui-test-manifest:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7122f04723363524ebfb93d2197207ef\transformed\ui-test-manifest-1.6.6\AndroidManifest.xml:25:13-36
102
103        <provider
103-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c99f34d6b8482bfcbb773832aa0cf7d\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
104            android:name="androidx.startup.InitializationProvider"
104-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c99f34d6b8482bfcbb773832aa0cf7d\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
105            android:authorities="com.example.myfirstapp.androidx-startup"
105-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c99f34d6b8482bfcbb773832aa0cf7d\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
106            android:exported="false" >
106-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c99f34d6b8482bfcbb773832aa0cf7d\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
107            <meta-data
107-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c99f34d6b8482bfcbb773832aa0cf7d\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
108                android:name="androidx.emoji2.text.EmojiCompatInitializer"
108-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c99f34d6b8482bfcbb773832aa0cf7d\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
109                android:value="androidx.startup" />
109-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c99f34d6b8482bfcbb773832aa0cf7d\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
110            <meta-data
110-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f1e6ac602e46892c730ac0637c95abd7\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:29:13-31:52
111                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
111-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f1e6ac602e46892c730ac0637c95abd7\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:30:17-78
112                android:value="androidx.startup" />
112-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f1e6ac602e46892c730ac0637c95abd7\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:31:17-49
113            <meta-data
113-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\453499689d447916c8e1e9c7fa388b45\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
114                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
114-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\453499689d447916c8e1e9c7fa388b45\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
115                android:value="androidx.startup" />
115-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\453499689d447916c8e1e9c7fa388b45\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
116        </provider>
117
118        <receiver
118-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\453499689d447916c8e1e9c7fa388b45\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
119            android:name="androidx.profileinstaller.ProfileInstallReceiver"
119-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\453499689d447916c8e1e9c7fa388b45\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
120            android:directBootAware="false"
120-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\453499689d447916c8e1e9c7fa388b45\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
121            android:enabled="true"
121-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\453499689d447916c8e1e9c7fa388b45\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
122            android:exported="true"
122-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\453499689d447916c8e1e9c7fa388b45\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
123            android:permission="android.permission.DUMP" >
123-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\453499689d447916c8e1e9c7fa388b45\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
124            <intent-filter>
124-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\453499689d447916c8e1e9c7fa388b45\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
125                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
125-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\453499689d447916c8e1e9c7fa388b45\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
125-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\453499689d447916c8e1e9c7fa388b45\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
126            </intent-filter>
127            <intent-filter>
127-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\453499689d447916c8e1e9c7fa388b45\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
128                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
128-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\453499689d447916c8e1e9c7fa388b45\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
128-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\453499689d447916c8e1e9c7fa388b45\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
129            </intent-filter>
130            <intent-filter>
130-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\453499689d447916c8e1e9c7fa388b45\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
131                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
131-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\453499689d447916c8e1e9c7fa388b45\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
131-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\453499689d447916c8e1e9c7fa388b45\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
132            </intent-filter>
133            <intent-filter>
133-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\453499689d447916c8e1e9c7fa388b45\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
134                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
134-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\453499689d447916c8e1e9c7fa388b45\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
134-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\453499689d447916c8e1e9c7fa388b45\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
135            </intent-filter>
136        </receiver>
137    </application>
138
139</manifest>
