-- Merging decision tree log ---
provider#androidx.core.content.FileProvider
INJECTED from C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:37:9-45:20
	android:grantUriPermissions
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:41:13-47
	android:authorities
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:39:13-60
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:40:13-37
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:38:13-62
manifest
ADDED from C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:2:1-78:12
INJECTED from C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:2:1-78:12
INJECTED from C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:2:1-78:12
INJECTED from C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:2:1-78:12
MERGED from [com.google.accompanist:accompanist-permissions:0.32.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e24a84933fb8da44bb0487d75abb5a4a\transformed\accompanist-permissions-0.32.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.compose.material3:material3-android:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bdae484d8629b91b71d2a8e711d605e\transformed\material3-release\AndroidManifest.xml:17:1-22:12
MERGED from [io.coil-kt:coil-compose:2.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\781233bd7e6038630e333ce86dd1eede\transformed\coil-compose-2.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [io.coil-kt:coil-compose-base:2.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\203cf16df820ebf245e3729ad96e2648\transformed\coil-compose-base-2.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d753ba01cbcf41a62ffa3740d4e0e54f\transformed\material-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-core-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\16c1161af96986b63ea5353ea7133d37\transformed\material-icons-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-extended-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e2721a18d1f384e325377e5ce198a2b2\transformed\material-icons-extended-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-ripple-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f07c7f5fd6d886868408f5a3c2274fff\transformed\material-ripple-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-core-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bf424f4bd841f98dbb7ceca5d9c6cfa1\transformed\animation-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1fa0e2b3020cb5ff555ca1cfb7c91698\transformed\animation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-layout-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9b11460080e1d7f0e385f7bcc26bd9b3\transformed\foundation-layout-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a575f2fdfb28ee7d8146806f00191903\transformed\foundation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f6fb8bd32822d6bb35cac9f5a3827fbe\transformed\ui-tooling-data-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-unit-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ac4c511404cbb5b7e9e3b5cb70751194\transformed\ui-unit-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-geometry-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\29b5a86133d992459d197d64e83d0fba\transformed\ui-geometry-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-util-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0b270d593ae2c6fd929e4f14fadb16de\transformed\ui-util-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-text-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7bc43ef36ef2e03e91db7842da6753a2\transformed\ui-text-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\97941244d0e9699216d8c0f799984a00\transformed\ui-tooling-preview-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d25cf0741a1dd4001a29bb4db0de5ca7\transformed\ui-tooling-release\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.compose.ui:ui-graphics-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8ac3969aad945845796070ea0de60daa\transformed\ui-graphics-release\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.accompanist:accompanist-drawablepainter:0.32.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5debcb88631276ca8f07e7207e977c67\transformed\accompanist-drawablepainter-0.32.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.compose.ui:ui-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d223efed7c9b71e5170a4e85d2ac6918\transformed\ui-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-test-manifest:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7122f04723363524ebfb93d2197207ef\transformed\ui-test-manifest-1.6.6\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca8518b89e1044a7f88162fa21c7ddc2\transformed\activity-1.10.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3fc2d3b173c418954d09a7bae00767a8\transformed\activity-ktx-1.10.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-compose:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f1c3c6de17e5be39a84fb800efecd44f\transformed\activity-compose-1.10.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5a5bbe3148462e0e1db275dd73c508e\transformed\autofill-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [io.coil-kt:coil:2.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed5492d570397412338b72eb7347e05f\transformed\coil-2.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [io.coil-kt:coil-base:2.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c79266dbb6554199e421e104c162c562\transformed\coil-base-2.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\09ea464a679a1471cb2f19d0b0fa51be\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c99f34d6b8482bfcbb773832aa0cf7d\transformed\emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d52224e5fd330b088bd38d8f43bb9982\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aeaec25a467dae6dd94d6019cb208e75\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f98ee3d67031989130b5c72a257577d6\transformed\core-1.15.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\10e4361f59b5abc9109bcf0935baef32\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7560741b5a7322061b41ba4c34c2636c\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f1e6ac602e46892c730ac0637c95abd7\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c48635e81582b82cb7579155e09e301d\transformed\lifecycle-livedata-core-2.8.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e3b7224d9a4a06d2f804fc2362d84c43\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e454d4ec182aa3378590e901caa08adb\transformed\lifecycle-runtime-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6c4aa4cb224cd949894a991718781104\transformed\lifecycle-viewmodel-ktx-2.8.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\27694cedfcff56ab6ae4cc0cbb1ef0c4\transformed\lifecycle-runtime-ktx-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\21267f4e2d604e13e521b01ab8944381\transformed\lifecycle-viewmodel-savedstate-2.8.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4b5ad589dfae2725b355be2db74e092c\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.core:core-ktx:1.15.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9b14facd5ab4179d25ad0ed95185a447\transformed\core-ktx-1.15.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3b0c039389b5e3daa74aff8a4f417a09\transformed\runtime-saveable-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fcd3ce12131302ec1fac3820c7a0e3fc\transformed\runtime-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\909784990c3d45f3f0fdd83263f9e641\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\453499689d447916c8e1e9c7fa388b45\transformed\profileinstaller-1.4.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f74880b7e4dc2ea23396745126b8a52\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\90bce9199b069f5c9c90b4c2e518facb\transformed\tracing-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e3cf8858ca664cf2a5302d3ed0998d6d\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c05cdca30afcb96efb3ce5510bbdeed3\transformed\exifinterface-1.3.6\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4ae577d56c7a6b6cbbd333be755128cb\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\35bb6a361aed974a27eced6880c0acd6\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [io.github.aakira:napier-android-debug:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3b80f225e3746e23d4db403f7e203e3f\transformed\napier-debug\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2f2db81535833d6680e5b05d3551a766\transformed\core-viewtree-1.0.0\AndroidManifest.xml:2:1-7:12
	package
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.CAMERA
ADDED from C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:6:5-64
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:6:22-62
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:7:5-79
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:7:22-77
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:8:5-80
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:8:22-78
uses-permission#android.permission.INTERNET
ADDED from C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:9:5-67
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:9:22-64
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:12:5-76
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:12:22-74
uses-permission#android.permission.FOREGROUND_SERVICE_CAMERA
ADDED from C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:13:5-83
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:13:22-81
uses-permission#android.permission.WAKE_LOCK
ADDED from C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:14:5-67
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:14:22-65
uses-permission#android.permission.POST_NOTIFICATIONS
ADDED from C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:15:5-76
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:15:22-74
uses-permission#android.permission.RECEIVE_BOOT_COMPLETED
ADDED from C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:18:5-80
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:18:22-78
uses-permission#android.permission.SYSTEM_ALERT_WINDOW
ADDED from C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:19:5-77
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:19:22-75
uses-feature#android.hardware.camera
ADDED from C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:22:5-83
	android:required
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:22:58-81
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:22:19-57
uses-feature#android.hardware.camera.autofocus
ADDED from C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:23:5-94
	android:required
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:23:68-92
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:23:19-67
application
ADDED from C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:25:5-76:19
INJECTED from C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:25:5-76:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d25cf0741a1dd4001a29bb4db0de5ca7\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d25cf0741a1dd4001a29bb4db0de5ca7\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7122f04723363524ebfb93d2197207ef\transformed\ui-test-manifest-1.6.6\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7122f04723363524ebfb93d2197207ef\transformed\ui-test-manifest-1.6.6\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c99f34d6b8482bfcbb773832aa0cf7d\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c99f34d6b8482bfcbb773832aa0cf7d\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f98ee3d67031989130b5c72a257577d6\transformed\core-1.15.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f98ee3d67031989130b5c72a257577d6\transformed\core-1.15.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f1e6ac602e46892c730ac0637c95abd7\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f1e6ac602e46892c730ac0637c95abd7\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\453499689d447916c8e1e9c7fa388b45\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\453499689d447916c8e1e9c7fa388b45\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f74880b7e4dc2ea23396745126b8a52\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f74880b7e4dc2ea23396745126b8a52\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e3cf8858ca664cf2a5302d3ed0998d6d\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e3cf8858ca664cf2a5302d3ed0998d6d\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
	android:extractNativeLibs
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f98ee3d67031989130b5c72a257577d6\transformed\core-1.15.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:32:9-35
	android:label
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:30:9-41
	android:fullBackupContent
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:28:9-54
	android:roundIcon
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:31:9-54
	tools:targetApi
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:34:9-29
	android:icon
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:29:9-43
	android:allowBackup
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:26:9-35
	android:theme
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:33:9-48
	android:dataExtractionRules
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:27:9-65
activity#com.example.myfirstapp.MainActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:47:9-56:20
	android:label
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:50:13-45
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:49:13-36
	android:theme
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:51:13-52
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:48:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:52:13-55:29
action#android.intent.action.MAIN
ADDED from C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:53:17-68
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:53:25-66
category#android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:54:17-76
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:54:27-74
service#com.example.myfirstapp.BackgroundCameraService
ADDED from C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:59:9-63:54
	android:enabled
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:61:13-35
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:62:13-37
	android:foregroundServiceType
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:63:13-51
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:60:13-52
receiver#com.example.myfirstapp.ScreenUnlockReceiver
ADDED from C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:66:9-75:20
	android:enabled
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:68:13-35
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:69:13-37
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:67:13-49
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.SCREEN_ON+action:name:android.intent.action.USER_PRESENT
ADDED from C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:70:13-74:29
	android:priority
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:70:28-51
action#android.intent.action.SCREEN_ON
ADDED from C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:71:17-73
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:71:25-71
action#android.intent.action.USER_PRESENT
ADDED from C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:72:17-76
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:72:25-74
action#android.intent.action.BOOT_COMPLETED
ADDED from C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:73:17-78
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:73:25-76
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:42:13-44:54
	android:resource
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:44:17-51
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml:43:17-67
uses-sdk
INJECTED from C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml
MERGED from [com.google.accompanist:accompanist-permissions:0.32.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e24a84933fb8da44bb0487d75abb5a4a\transformed\accompanist-permissions-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.accompanist:accompanist-permissions:0.32.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e24a84933fb8da44bb0487d75abb5a4a\transformed\accompanist-permissions-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.compose.material3:material3-android:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bdae484d8629b91b71d2a8e711d605e\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3-android:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bdae484d8629b91b71d2a8e711d605e\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [io.coil-kt:coil-compose:2.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\781233bd7e6038630e333ce86dd1eede\transformed\coil-compose-2.5.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-compose:2.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\781233bd7e6038630e333ce86dd1eede\transformed\coil-compose-2.5.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-compose-base:2.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\203cf16df820ebf245e3729ad96e2648\transformed\coil-compose-base-2.5.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-compose-base:2.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\203cf16df820ebf245e3729ad96e2648\transformed\coil-compose-base-2.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d753ba01cbcf41a62ffa3740d4e0e54f\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d753ba01cbcf41a62ffa3740d4e0e54f\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\16c1161af96986b63ea5353ea7133d37\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\16c1161af96986b63ea5353ea7133d37\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-extended-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e2721a18d1f384e325377e5ce198a2b2\transformed\material-icons-extended-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-extended-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e2721a18d1f384e325377e5ce198a2b2\transformed\material-icons-extended-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f07c7f5fd6d886868408f5a3c2274fff\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f07c7f5fd6d886868408f5a3c2274fff\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bf424f4bd841f98dbb7ceca5d9c6cfa1\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bf424f4bd841f98dbb7ceca5d9c6cfa1\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1fa0e2b3020cb5ff555ca1cfb7c91698\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1fa0e2b3020cb5ff555ca1cfb7c91698\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9b11460080e1d7f0e385f7bcc26bd9b3\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9b11460080e1d7f0e385f7bcc26bd9b3\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a575f2fdfb28ee7d8146806f00191903\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a575f2fdfb28ee7d8146806f00191903\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f6fb8bd32822d6bb35cac9f5a3827fbe\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f6fb8bd32822d6bb35cac9f5a3827fbe\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ac4c511404cbb5b7e9e3b5cb70751194\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ac4c511404cbb5b7e9e3b5cb70751194\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\29b5a86133d992459d197d64e83d0fba\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\29b5a86133d992459d197d64e83d0fba\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0b270d593ae2c6fd929e4f14fadb16de\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0b270d593ae2c6fd929e4f14fadb16de\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7bc43ef36ef2e03e91db7842da6753a2\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7bc43ef36ef2e03e91db7842da6753a2\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\97941244d0e9699216d8c0f799984a00\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\97941244d0e9699216d8c0f799984a00\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d25cf0741a1dd4001a29bb4db0de5ca7\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d25cf0741a1dd4001a29bb4db0de5ca7\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8ac3969aad945845796070ea0de60daa\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8ac3969aad945845796070ea0de60daa\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [com.google.accompanist:accompanist-drawablepainter:0.32.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5debcb88631276ca8f07e7207e977c67\transformed\accompanist-drawablepainter-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.accompanist:accompanist-drawablepainter:0.32.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5debcb88631276ca8f07e7207e977c67\transformed\accompanist-drawablepainter-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.compose.ui:ui-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d223efed7c9b71e5170a4e85d2ac6918\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d223efed7c9b71e5170a4e85d2ac6918\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7122f04723363524ebfb93d2197207ef\transformed\ui-test-manifest-1.6.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7122f04723363524ebfb93d2197207ef\transformed\ui-test-manifest-1.6.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca8518b89e1044a7f88162fa21c7ddc2\transformed\activity-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca8518b89e1044a7f88162fa21c7ddc2\transformed\activity-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3fc2d3b173c418954d09a7bae00767a8\transformed\activity-ktx-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3fc2d3b173c418954d09a7bae00767a8\transformed\activity-ktx-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f1c3c6de17e5be39a84fb800efecd44f\transformed\activity-compose-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f1c3c6de17e5be39a84fb800efecd44f\transformed\activity-compose-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5a5bbe3148462e0e1db275dd73c508e\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5a5bbe3148462e0e1db275dd73c508e\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [io.coil-kt:coil:2.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed5492d570397412338b72eb7347e05f\transformed\coil-2.5.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil:2.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed5492d570397412338b72eb7347e05f\transformed\coil-2.5.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-base:2.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c79266dbb6554199e421e104c162c562\transformed\coil-base-2.5.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-base:2.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c79266dbb6554199e421e104c162c562\transformed\coil-base-2.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\09ea464a679a1471cb2f19d0b0fa51be\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\09ea464a679a1471cb2f19d0b0fa51be\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c99f34d6b8482bfcbb773832aa0cf7d\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c99f34d6b8482bfcbb773832aa0cf7d\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d52224e5fd330b088bd38d8f43bb9982\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d52224e5fd330b088bd38d8f43bb9982\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aeaec25a467dae6dd94d6019cb208e75\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aeaec25a467dae6dd94d6019cb208e75\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f98ee3d67031989130b5c72a257577d6\transformed\core-1.15.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f98ee3d67031989130b5c72a257577d6\transformed\core-1.15.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\10e4361f59b5abc9109bcf0935baef32\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\10e4361f59b5abc9109bcf0935baef32\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7560741b5a7322061b41ba4c34c2636c\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7560741b5a7322061b41ba4c34c2636c\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f1e6ac602e46892c730ac0637c95abd7\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f1e6ac602e46892c730ac0637c95abd7\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c48635e81582b82cb7579155e09e301d\transformed\lifecycle-livedata-core-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c48635e81582b82cb7579155e09e301d\transformed\lifecycle-livedata-core-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e3b7224d9a4a06d2f804fc2362d84c43\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e3b7224d9a4a06d2f804fc2362d84c43\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e454d4ec182aa3378590e901caa08adb\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e454d4ec182aa3378590e901caa08adb\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6c4aa4cb224cd949894a991718781104\transformed\lifecycle-viewmodel-ktx-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6c4aa4cb224cd949894a991718781104\transformed\lifecycle-viewmodel-ktx-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\27694cedfcff56ab6ae4cc0cbb1ef0c4\transformed\lifecycle-runtime-ktx-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\27694cedfcff56ab6ae4cc0cbb1ef0c4\transformed\lifecycle-runtime-ktx-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\21267f4e2d604e13e521b01ab8944381\transformed\lifecycle-viewmodel-savedstate-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\21267f4e2d604e13e521b01ab8944381\transformed\lifecycle-viewmodel-savedstate-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4b5ad589dfae2725b355be2db74e092c\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4b5ad589dfae2725b355be2db74e092c\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.core:core-ktx:1.15.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9b14facd5ab4179d25ad0ed95185a447\transformed\core-ktx-1.15.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.15.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9b14facd5ab4179d25ad0ed95185a447\transformed\core-ktx-1.15.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3b0c039389b5e3daa74aff8a4f417a09\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3b0c039389b5e3daa74aff8a4f417a09\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fcd3ce12131302ec1fac3820c7a0e3fc\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fcd3ce12131302ec1fac3820c7a0e3fc\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\909784990c3d45f3f0fdd83263f9e641\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\909784990c3d45f3f0fdd83263f9e641\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\453499689d447916c8e1e9c7fa388b45\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\453499689d447916c8e1e9c7fa388b45\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f74880b7e4dc2ea23396745126b8a52\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f74880b7e4dc2ea23396745126b8a52\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\90bce9199b069f5c9c90b4c2e518facb\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\90bce9199b069f5c9c90b4c2e518facb\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e3cf8858ca664cf2a5302d3ed0998d6d\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e3cf8858ca664cf2a5302d3ed0998d6d\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c05cdca30afcb96efb3ce5510bbdeed3\transformed\exifinterface-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c05cdca30afcb96efb3ce5510bbdeed3\transformed\exifinterface-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4ae577d56c7a6b6cbbd333be755128cb\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4ae577d56c7a6b6cbbd333be755128cb\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\35bb6a361aed974a27eced6880c0acd6\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\35bb6a361aed974a27eced6880c0acd6\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [io.github.aakira:napier-android-debug:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3b80f225e3746e23d4db403f7e203e3f\transformed\napier-debug\AndroidManifest.xml:7:5-9:41
MERGED from [io.github.aakira:napier-android-debug:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3b80f225e3746e23d4db403f7e203e3f\transformed\napier-debug\AndroidManifest.xml:7:5-9:41
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2f2db81535833d6680e5b05d3551a766\transformed\core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2f2db81535833d6680e5b05d3551a766\transformed\core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\MyFirstApp\app\src\main\AndroidManifest.xml
activity#androidx.compose.ui.tooling.PreviewActivity
ADDED from [androidx.compose.ui:ui-tooling-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d25cf0741a1dd4001a29bb4db0de5ca7\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-tooling-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d25cf0741a1dd4001a29bb4db0de5ca7\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-tooling-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d25cf0741a1dd4001a29bb4db0de5ca7\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
activity#androidx.activity.ComponentActivity
ADDED from [androidx.compose.ui:ui-test-manifest:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7122f04723363524ebfb93d2197207ef\transformed\ui-test-manifest-1.6.6\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-test-manifest:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7122f04723363524ebfb93d2197207ef\transformed\ui-test-manifest-1.6.6\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-test-manifest:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7122f04723363524ebfb93d2197207ef\transformed\ui-test-manifest-1.6.6\AndroidManifest.xml:24:13-63
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c99f34d6b8482bfcbb773832aa0cf7d\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f1e6ac602e46892c730ac0637c95abd7\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f1e6ac602e46892c730ac0637c95abd7\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\453499689d447916c8e1e9c7fa388b45\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\453499689d447916c8e1e9c7fa388b45\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f74880b7e4dc2ea23396745126b8a52\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f74880b7e4dc2ea23396745126b8a52\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c99f34d6b8482bfcbb773832aa0cf7d\transformed\emoji2-1.3.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c99f34d6b8482bfcbb773832aa0cf7d\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c99f34d6b8482bfcbb773832aa0cf7d\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c99f34d6b8482bfcbb773832aa0cf7d\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c99f34d6b8482bfcbb773832aa0cf7d\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c99f34d6b8482bfcbb773832aa0cf7d\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c99f34d6b8482bfcbb773832aa0cf7d\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f98ee3d67031989130b5c72a257577d6\transformed\core-1.15.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f98ee3d67031989130b5c72a257577d6\transformed\core-1.15.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f98ee3d67031989130b5c72a257577d6\transformed\core-1.15.0\AndroidManifest.xml:23:9-81
permission#com.example.myfirstapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f98ee3d67031989130b5c72a257577d6\transformed\core-1.15.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f98ee3d67031989130b5c72a257577d6\transformed\core-1.15.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f98ee3d67031989130b5c72a257577d6\transformed\core-1.15.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f98ee3d67031989130b5c72a257577d6\transformed\core-1.15.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f98ee3d67031989130b5c72a257577d6\transformed\core-1.15.0\AndroidManifest.xml:26:22-94
uses-permission#com.example.myfirstapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f98ee3d67031989130b5c72a257577d6\transformed\core-1.15.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f98ee3d67031989130b5c72a257577d6\transformed\core-1.15.0\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f1e6ac602e46892c730ac0637c95abd7\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f1e6ac602e46892c730ac0637c95abd7\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f1e6ac602e46892c730ac0637c95abd7\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:30:17-78
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\453499689d447916c8e1e9c7fa388b45\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\453499689d447916c8e1e9c7fa388b45\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\453499689d447916c8e1e9c7fa388b45\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\453499689d447916c8e1e9c7fa388b45\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\453499689d447916c8e1e9c7fa388b45\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\453499689d447916c8e1e9c7fa388b45\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\453499689d447916c8e1e9c7fa388b45\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\453499689d447916c8e1e9c7fa388b45\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\453499689d447916c8e1e9c7fa388b45\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\453499689d447916c8e1e9c7fa388b45\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\453499689d447916c8e1e9c7fa388b45\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\453499689d447916c8e1e9c7fa388b45\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\453499689d447916c8e1e9c7fa388b45\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\453499689d447916c8e1e9c7fa388b45\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\453499689d447916c8e1e9c7fa388b45\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\453499689d447916c8e1e9c7fa388b45\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\453499689d447916c8e1e9c7fa388b45\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\453499689d447916c8e1e9c7fa388b45\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\453499689d447916c8e1e9c7fa388b45\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\453499689d447916c8e1e9c7fa388b45\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\453499689d447916c8e1e9c7fa388b45\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
