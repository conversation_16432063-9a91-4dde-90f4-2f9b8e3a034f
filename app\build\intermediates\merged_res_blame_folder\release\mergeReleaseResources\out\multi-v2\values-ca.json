{"logs": [{"outputFile": "com.example.myfirstapp-mergeReleaseResources-43:/values-ca/values-ca.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\3bdae484d8629b91b71d2a8e711d605e\\transformed\\material3-release\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,176,295,414,534,634,732,847,989,1104,1263,1347,1445,1543,1644,1761,1890,1993,2134,2274,2415,2581,2714,2831,2952,3081,3180,3277,3398,3543,3649,3762,3876,4015,4160,4269,4376,4462,4563,4664,4750,4836,4947,5027,5111,5212,5320,5419,5523,5610,5723,5823,5930,6049,6129,6246", "endColumns": "120,118,118,119,99,97,114,141,114,158,83,97,97,100,116,128,102,140,139,140,165,132,116,120,128,98,96,120,144,105,112,113,138,144,108,106,85,100,100,85,85,110,79,83,100,107,98,103,86,112,99,106,118,79,116,106", "endOffsets": "171,290,409,529,629,727,842,984,1099,1258,1342,1440,1538,1639,1756,1885,1988,2129,2269,2410,2576,2709,2826,2947,3076,3175,3272,3393,3538,3644,3757,3871,4010,4155,4264,4371,4457,4558,4659,4745,4831,4942,5022,5106,5207,5315,5414,5518,5605,5718,5818,5925,6044,6124,6241,6348"}, "to": {"startLines": "16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1482,1603,1722,1841,1961,2061,2159,2274,2416,2531,2690,2774,2872,2970,3071,3188,3317,3420,3561,3701,3842,4008,4141,4258,4379,4508,4607,4704,4825,4970,5076,5189,5303,5442,5587,5696,5803,5889,5990,6091,6177,6263,6374,6454,6538,6639,6747,6846,6950,7037,7150,7250,7357,7476,7556,7673", "endColumns": "120,118,118,119,99,97,114,141,114,158,83,97,97,100,116,128,102,140,139,140,165,132,116,120,128,98,96,120,144,105,112,113,138,144,108,106,85,100,100,85,85,110,79,83,100,107,98,103,86,112,99,106,118,79,116,106", "endOffsets": "1598,1717,1836,1956,2056,2154,2269,2411,2526,2685,2769,2867,2965,3066,3183,3312,3415,3556,3696,3837,4003,4136,4253,4374,4503,4602,4699,4820,4965,5071,5184,5298,5437,5582,5691,5798,5884,5985,6086,6172,6258,6369,6449,6533,6634,6742,6841,6945,7032,7145,7245,7352,7471,7551,7668,7775"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f98ee3d67031989130b5c72a257577d6\\transformed\\core-1.15.0\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,449,555,660,786", "endColumns": "95,101,98,96,105,104,125,100", "endOffsets": "146,248,347,444,550,655,781,882"}, "to": {"startLines": "2,3,4,5,6,7,8,79", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,201,303,402,499,605,710,8351", "endColumns": "95,101,98,96,105,104,125,100", "endOffsets": "196,298,397,494,600,705,831,8447"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\d223efed7c9b71e5170a4e85d2ac6918\\transformed\\ui-release\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,286,390,493,582,660,751,842,928,1000,1069,1155,1246,1322,1404,1475", "endColumns": "96,83,103,102,88,77,90,90,85,71,68,85,90,75,81,70,119", "endOffsets": "197,281,385,488,577,655,746,837,923,995,1064,1150,1241,1317,1399,1470,1590"}, "to": {"startLines": "9,10,11,12,13,14,15,72,73,74,75,76,77,78,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "836,933,1017,1121,1224,1313,1391,7780,7871,7957,8029,8098,8184,8275,8452,8534,8605", "endColumns": "96,83,103,102,88,77,90,90,85,71,68,85,90,75,81,70,119", "endOffsets": "928,1012,1116,1219,1308,1386,1477,7866,7952,8024,8093,8179,8270,8346,8529,8600,8720"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a575f2fdfb28ee7d8146806f00191903\\transformed\\foundation-release\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,153", "endColumns": "97,101", "endOffsets": "148,250"}, "to": {"startLines": "83,84", "startColumns": "4,4", "startOffsets": "8725,8823", "endColumns": "97,101", "endOffsets": "8818,8920"}}]}]}