package com.example.myfirstapp

import android.Manifest
import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.app.Service
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.graphics.ImageFormat
import android.hardware.camera2.*
import android.media.ImageReader
import android.os.Build
import android.os.Handler
import android.os.HandlerThread
import android.os.IBinder
import android.util.Log
import android.util.Size
import android.view.Surface
import androidx.core.app.ActivityCompat
import androidx.core.app.NotificationCompat
import kotlinx.coroutines.*
import java.io.File
import java.io.FileOutputStream
import java.util.*
import java.util.concurrent.Semaphore
import java.util.concurrent.TimeUnit

class BackgroundCameraService : Service() {

    companion object {
        private const val TAG = "BackgroundCameraService"
        private const val NOTIFICATION_ID = 1001
        private const val CHANNEL_ID = "background_camera_channel"

        const val ACTION_START_SERVICE = "START_SERVICE"
        const val ACTION_STOP_SERVICE = "STOP_SERVICE"
        const val ACTION_CAPTURE_NOW = "CAPTURE_NOW"
    }

    private var cameraManager: CameraManager? = null
    private var cameraDevice: CameraDevice? = null
    private var captureSession: CameraCaptureSession? = null
    private var imageReader: ImageReader? = null
    private var backgroundHandler: Handler? = null
    private var backgroundThread: HandlerThread? = null
    private val cameraOpenCloseLock = Semaphore(1)

    private var isServiceRunning = false
    private val serviceScope = CoroutineScope(Dispatchers.Main + SupervisorJob())
    private var captureJob: Job? = null

    override fun onCreate() {
        super.onCreate()
        Log.d(TAG, "Service created")
        createNotificationChannel()
        startBackgroundThread()
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        when (intent?.action) {
            ACTION_START_SERVICE -> startCameraService()
            ACTION_STOP_SERVICE -> stopCameraService()
            ACTION_CAPTURE_NOW -> {
                Log.d(TAG, "📸 Immediate capture requested (device unlocked)")
                if (isServiceRunning) {
                    captureStillPicture()
                } else {
                    // Start service and capture immediately
                    startCameraService()
                    // Capture will happen once camera is ready
                }
            }
        }
        return START_STICKY
    }

    override fun onBind(intent: Intent?): IBinder? = null

    private fun startCameraService() {
        if (isServiceRunning) return

        Log.d(TAG, "Starting camera service")
        isServiceRunning = true

        val notification = createNotification("📱 Unlock Detection Active", "Front camera captures when device unlocks")
        startForeground(NOTIFICATION_ID, notification)

        // Check permissions
        if (!hasRequiredPermissions()) {
            Log.e(TAG, "Missing required permissions")
            stopSelf()
            return
        }

        // Initialize camera
        initializeCamera()
    }

    private fun stopCameraService() {
        Log.d(TAG, "Stopping camera service")
        isServiceRunning = false

        captureJob?.cancel()
        closeCamera()
        stopForeground(true)
        stopSelf()
    }

    private fun hasRequiredPermissions(): Boolean {
        return ActivityCompat.checkSelfPermission(this, Manifest.permission.CAMERA) == PackageManager.PERMISSION_GRANTED
    }

    private fun initializeCamera() {
        cameraManager = getSystemService(Context.CAMERA_SERVICE) as CameraManager

        try {
            val cameraId = getCameraId()
            if (cameraId != null) {
                openCamera(cameraId)
            } else {
                Log.e(TAG, "No suitable camera found")
                stopSelf()
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error initializing camera", e)
            stopSelf()
        }
    }

    private fun getCameraId(): String? {
        return try {
            cameraManager?.cameraIdList?.firstOrNull { cameraId ->
                val characteristics = cameraManager?.getCameraCharacteristics(cameraId)
                val facing = characteristics?.get(CameraCharacteristics.LENS_FACING)
                // Use FRONT camera instead of back camera
                facing == CameraCharacteristics.LENS_FACING_FRONT
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error getting front camera ID", e)
            null
        }
    }

    private fun openCamera(cameraId: String) {
        try {
            if (!cameraOpenCloseLock.tryAcquire(2500, TimeUnit.MILLISECONDS)) {
                throw RuntimeException("Time out waiting to lock camera opening.")
            }

            if (ActivityCompat.checkSelfPermission(this, Manifest.permission.CAMERA) != PackageManager.PERMISSION_GRANTED) {
                return
            }

            cameraManager?.openCamera(cameraId, stateCallback, backgroundHandler)
        } catch (e: Exception) {
            Log.e(TAG, "Error opening camera", e)
            cameraOpenCloseLock.release()
        }
    }

    private val stateCallback = object : CameraDevice.StateCallback() {
        override fun onOpened(camera: CameraDevice) {
            Log.d(TAG, "Front camera opened successfully")
            cameraOpenCloseLock.release()
            cameraDevice = camera
            createCameraPreviewSession()
        }

        override fun onDisconnected(camera: CameraDevice) {
            Log.d(TAG, "Camera disconnected")
            cameraOpenCloseLock.release()
            camera.close()
            cameraDevice = null
        }

        override fun onError(camera: CameraDevice, error: Int) {
            Log.e(TAG, "Camera error: $error")
            cameraOpenCloseLock.release()
            camera.close()
            cameraDevice = null
        }
    }

    private fun createCameraPreviewSession() {
        try {
            val camera = cameraDevice ?: return

            // Set up ImageReader for capturing images
            imageReader = ImageReader.newInstance(1920, 1080, ImageFormat.JPEG, 1)
            imageReader?.setOnImageAvailableListener(imageAvailableListener, backgroundHandler)

            val surfaces = listOf<Surface>(imageReader?.surface!!)

            camera.createCaptureSession(surfaces, object : CameraCaptureSession.StateCallback() {
                override fun onConfigured(session: CameraCaptureSession) {
                    Log.d(TAG, "Camera capture session configured")
                    captureSession = session
                }

                override fun onConfigureFailed(session: CameraCaptureSession) {
                    Log.e(TAG, "Failed to configure camera capture session")
                }
            }, backgroundHandler)

        } catch (e: Exception) {
            Log.e(TAG, "Error creating camera preview session", e)
        }
    }

    private val imageAvailableListener = ImageReader.OnImageAvailableListener { reader ->
        val image = reader.acquireLatestImage()
        if (image != null) {
            backgroundHandler?.post {
                saveImageAndUpload(image)
                image.close()
            }
        }
    }

    private fun saveImageAndUpload(image: android.media.Image) {
        try {
            val buffer = image.planes[0].buffer
            val bytes = ByteArray(buffer.remaining())
            buffer.get(bytes)

            val timestamp = System.currentTimeMillis()
            val fileName = "unlock_capture_$timestamp.jpg"
            val file = File(getExternalFilesDir(null), fileName)

            FileOutputStream(file).use { output ->
                output.write(bytes)
            }

            Log.d(TAG, "Image saved: ${file.absolutePath}")

            // Upload to Cloudinary
            serviceScope.launch {
                uploadImageToCloudinary(
                    context = this@BackgroundCameraService,
                    imageFile = file,
                    onStart = {
                        updateNotification("📤 Uploading...", "Front camera capture from unlock")
                    },
                    onSuccess = { url ->
                        Log.d(TAG, "Unlock capture uploaded successfully: $url")
                        updateNotification("✅ Upload successful", "Monitoring for next unlock")
                        // Delete local file after successful upload
                        file.delete()
                    },
                    onError = { error ->
                        Log.e(TAG, "Upload failed: $error")
                        updateNotification("❌ Upload failed", "Will retry on next unlock")
                    }
                )
            }

        } catch (e: Exception) {
            Log.e(TAG, "Error saving image", e)
        }
    }

    private fun captureStillPicture() {
        try {
            val camera = cameraDevice ?: return
            val session = captureSession ?: return
            val reader = imageReader ?: return

            val captureBuilder = camera.createCaptureRequest(CameraDevice.TEMPLATE_STILL_CAPTURE)
            captureBuilder.addTarget(reader.surface)

            // Auto focus and flash
            captureBuilder.set(CaptureRequest.CONTROL_AF_MODE, CaptureRequest.CONTROL_AF_MODE_CONTINUOUS_PICTURE)
            captureBuilder.set(CaptureRequest.CONTROL_AE_MODE, CaptureRequest.CONTROL_AE_MODE_ON_AUTO_FLASH)

            session.capture(captureBuilder.build(), null, backgroundHandler)
            Log.d(TAG, "📸 Front camera capture request sent (unlock triggered)")

        } catch (e: Exception) {
            Log.e(TAG, "Error capturing still picture", e)
        }
    }



    private fun closeCamera() {
        try {
            cameraOpenCloseLock.acquire()
            captureSession?.close()
            captureSession = null
            cameraDevice?.close()
            cameraDevice = null
            imageReader?.close()
            imageReader = null
        } catch (e: InterruptedException) {
            Log.e(TAG, "Interrupted while trying to lock camera closing", e)
        } finally {
            cameraOpenCloseLock.release()
        }
    }

    private fun startBackgroundThread() {
        backgroundThread = HandlerThread("CameraBackground").also { it.start() }
        backgroundHandler = Handler(backgroundThread?.looper!!)
    }

    private fun stopBackgroundThread() {
        backgroundThread?.quitSafely()
        try {
            backgroundThread?.join()
            backgroundThread = null
            backgroundHandler = null
        } catch (e: InterruptedException) {
            Log.e(TAG, "Error stopping background thread", e)
        }
    }

    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                CHANNEL_ID,
                "Background Camera Service",
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = "Automatic photo capture service"
                setShowBadge(false)
            }

            val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.createNotificationChannel(channel)
        }
    }

    private fun createNotification(title: String, content: String): Notification {
        val stopIntent = Intent(this, BackgroundCameraService::class.java).apply {
            action = ACTION_STOP_SERVICE
        }
        val stopPendingIntent = PendingIntent.getService(
            this, 0, stopIntent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        val mainIntent = Intent(this, MainActivity::class.java)
        val mainPendingIntent = PendingIntent.getActivity(
            this, 0, mainIntent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        return NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle(title)
            .setContentText(content)
            .setSmallIcon(android.R.drawable.ic_menu_camera)
            .setContentIntent(mainPendingIntent)
            .addAction(android.R.drawable.ic_menu_close_clear_cancel, "Stop", stopPendingIntent)
            .setOngoing(true)
            .build()
    }

    private fun updateNotification(title: String, content: String) {
        val notification = createNotification(title, content)
        val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        notificationManager.notify(NOTIFICATION_ID, notification)
    }

    override fun onDestroy() {
        super.onDestroy()
        Log.d(TAG, "Service destroyed")
        isServiceRunning = false
        captureJob?.cancel()
        serviceScope.cancel()
        closeCamera()
        stopBackgroundThread()
    }
}
