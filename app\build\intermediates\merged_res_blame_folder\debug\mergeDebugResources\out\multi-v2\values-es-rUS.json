{"logs": [{"outputFile": "com.example.myfirstapp-mergeDebugResources-47:/values-es-rUS/values-es-rUS.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\3bdae484d8629b91b71d2a8e711d605e\\transformed\\material3-release\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,177,299,422,542,642,740,855,998,1116,1268,1353,1455,1552,1654,1772,1895,2002,2138,2271,2410,2592,2723,2843,2965,3092,3190,3286,3407,3540,3641,3746,3861,3996,4137,4248,4353,4430,4526,4621,4708,4797,4908,4988,5072,5173,5279,5379,5478,5566,5681,5782,5886,6009,6089,6196", "endColumns": "121,121,122,119,99,97,114,142,117,151,84,101,96,101,117,122,106,135,132,138,181,130,119,121,126,97,95,120,132,100,104,114,134,140,110,104,76,95,94,86,88,110,79,83,100,105,99,98,87,114,100,103,122,79,106,98", "endOffsets": "172,294,417,537,637,735,850,993,1111,1263,1348,1450,1547,1649,1767,1890,1997,2133,2266,2405,2587,2718,2838,2960,3087,3185,3281,3402,3535,3636,3741,3856,3991,4132,4243,4348,4425,4521,4616,4703,4792,4903,4983,5067,5168,5274,5374,5473,5561,5676,5777,5881,6004,6084,6191,6290"}, "to": {"startLines": "16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1483,1605,1727,1850,1970,2070,2168,2283,2426,2544,2696,2781,2883,2980,3082,3200,3323,3430,3566,3699,3838,4020,4151,4271,4393,4520,4618,4714,4835,4968,5069,5174,5289,5424,5565,5676,5781,5858,5954,6049,6136,6225,6336,6416,6500,6601,6707,6807,6906,6994,7109,7210,7314,7437,7517,7624", "endColumns": "121,121,122,119,99,97,114,142,117,151,84,101,96,101,117,122,106,135,132,138,181,130,119,121,126,97,95,120,132,100,104,114,134,140,110,104,76,95,94,86,88,110,79,83,100,105,99,98,87,114,100,103,122,79,106,98", "endOffsets": "1600,1722,1845,1965,2065,2163,2278,2421,2539,2691,2776,2878,2975,3077,3195,3318,3425,3561,3694,3833,4015,4146,4266,4388,4515,4613,4709,4830,4963,5064,5169,5284,5419,5560,5671,5776,5853,5949,6044,6131,6220,6331,6411,6495,6596,6702,6802,6901,6989,7104,7205,7309,7432,7512,7619,7718"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\d223efed7c9b71e5170a4e85d2ac6918\\transformed\\ui-release\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,204,286,384,487,576,655,751,843,930,994,1058,1145,1235,1312,1390,1460", "endColumns": "98,81,97,102,88,78,95,91,86,63,63,86,89,76,77,69,122", "endOffsets": "199,281,379,482,571,650,746,838,925,989,1053,1140,1230,1307,1385,1455,1578"}, "to": {"startLines": "9,10,11,12,13,14,15,72,73,74,75,76,77,78,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "837,936,1018,1116,1219,1308,1387,7723,7815,7902,7966,8030,8117,8207,8385,8463,8533", "endColumns": "98,81,97,102,88,78,95,91,86,63,63,86,89,76,77,69,122", "endOffsets": "931,1013,1111,1214,1303,1382,1478,7810,7897,7961,8025,8112,8202,8279,8458,8528,8651"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a575f2fdfb28ee7d8146806f00191903\\transformed\\foundation-release\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,155", "endColumns": "99,101", "endOffsets": "150,252"}, "to": {"startLines": "83,84", "startColumns": "4,4", "startOffsets": "8656,8756", "endColumns": "99,101", "endOffsets": "8751,8853"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f98ee3d67031989130b5c72a257577d6\\transformed\\core-1.15.0\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,356,454,561,667,787", "endColumns": "98,101,99,97,106,105,119,100", "endOffsets": "149,251,351,449,556,662,782,883"}, "to": {"startLines": "2,3,4,5,6,7,8,79", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,204,306,406,504,611,717,8284", "endColumns": "98,101,99,97,106,105,119,100", "endOffsets": "199,301,401,499,606,712,832,8380"}}]}]}