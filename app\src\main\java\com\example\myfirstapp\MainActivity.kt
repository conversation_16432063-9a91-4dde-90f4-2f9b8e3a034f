package com.example.myfirstapp

import android.Manifest
import android.content.ContentResolver
import android.content.Context
import android.net.Uri
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.compose.setContent
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.Camera
import androidx.compose.material.icons.filled.Photo
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.core.content.FileProvider
import coil.compose.rememberAsyncImagePainter
import com.example.myfirstapp.ui.theme.MyFirstAppTheme
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import okhttp3.*
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.RequestBody.Companion.asRequestBody
import org.json.JSONObject
import java.io.File
import java.io.FileOutputStream
import java.io.IOException
import java.io.InputStream

class MainActivity : ComponentActivity() {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContent {
            MyFirstAppTheme {
                ImageUploadApp()
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ImageUploadApp() {
    val context = LocalContext.current
    val coroutineScope = rememberCoroutineScope()

    // State
    var uploadedImageUrls by remember { mutableStateOf(listOf<String>()) }
    var isUploading by remember { mutableStateOf(false) }
    var showBottomSheet by remember { mutableStateOf(false) }
    val snackbarHostState = remember { SnackbarHostState() }

    // Create image file for camera
    val imageFile = remember {
        File(context.getExternalFilesDir(null), "captured_photo_${System.currentTimeMillis()}.jpg")
    }
    val imageUri = remember(imageFile) {
        FileProvider.getUriForFile(context, "${context.packageName}.provider", imageFile)
    }

    // Camera launcher
    val takePictureLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.TakePicture()
    ) { success ->
        if (success) {
            coroutineScope.launch {
                uploadImageToCloudinary(
                    context = context,
                    imageFile = imageFile,
                    onStart = { isUploading = true },
                    onSuccess = { url ->
                        uploadedImageUrls = uploadedImageUrls + url
                        isUploading = false
                        coroutineScope.launch {
                            snackbarHostState.showSnackbar("✅ Image uploaded successfully!")
                        }
                    },
                    onError = { error ->
                        isUploading = false
                        coroutineScope.launch {
                            snackbarHostState.showSnackbar("❌ Upload failed: $error")
                        }
                    }
                )
            }
        }
    }

    // Gallery launcher
    val galleryLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.GetContent()
    ) { uri ->
        uri?.let {
            coroutineScope.launch {
                val tempFile = copyUriToFile(context, uri)
                if (tempFile != null) {
                    uploadImageToCloudinary(
                        context = context,
                        imageFile = tempFile,
                        onStart = { isUploading = true },
                        onSuccess = { url ->
                            uploadedImageUrls = uploadedImageUrls + url
                            isUploading = false
                            coroutineScope.launch {
                                snackbarHostState.showSnackbar("✅ Image uploaded successfully!")
                            }
                        },
                        onError = { error ->
                            isUploading = false
                            coroutineScope.launch {
                                snackbarHostState.showSnackbar("❌ Upload failed: $error")
                            }
                        }
                    )
                } else {
                    coroutineScope.launch {
                        snackbarHostState.showSnackbar("❌ Failed to process selected image")
                    }
                }
            }
        }
    }

    // Camera permission launcher
    val cameraPermissionLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.RequestPermission()
    ) { granted ->
        if (granted) {
            takePictureLauncher.launch(imageUri)
        } else {
            coroutineScope.launch {
                snackbarHostState.showSnackbar("Camera permission is required to take photos")
            }
        }
    }

    // Bottom Sheet for image selection
    if (showBottomSheet) {
        ModalBottomSheet(
            onDismissRequest = { showBottomSheet = false }
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp)
            ) {
                Text(
                    text = "Select Image Source",
                    style = MaterialTheme.typography.headlineSmall,
                    fontWeight = FontWeight.Bold,
                    modifier = Modifier.padding(bottom = 16.dp)
                )

                // Camera option
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .clickable {
                            showBottomSheet = false
                            cameraPermissionLauncher.launch(Manifest.permission.CAMERA)
                        }
                        .padding(vertical = 12.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        imageVector = Icons.Default.Camera,
                        contentDescription = "Camera",
                        modifier = Modifier.padding(end = 16.dp)
                    )
                    Text(
                        text = "Take Photo",
                        style = MaterialTheme.typography.bodyLarge
                    )
                }

                // Gallery option
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .clickable {
                            showBottomSheet = false
                            galleryLauncher.launch("image/*")
                        }
                        .padding(vertical = 12.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        imageVector = Icons.Default.Photo,
                        contentDescription = "Gallery",
                        modifier = Modifier.padding(end = 16.dp)
                    )
                    Text(
                        text = "Choose from Gallery",
                        style = MaterialTheme.typography.bodyLarge
                    )
                }

                Spacer(modifier = Modifier.height(16.dp))
            }
        }
    }

    // Main UI
    Scaffold(
        snackbarHost = { SnackbarHost(snackbarHostState) },
        floatingActionButton = {
            FloatingActionButton(
                onClick = { showBottomSheet = true },
                modifier = Modifier.padding(16.dp)
            ) {
                if (isUploading) {
                    CircularProgressIndicator(
                        modifier = Modifier.size(24.dp),
                        color = MaterialTheme.colorScheme.onPrimary
                    )
                } else {
                    Icon(
                        imageVector = Icons.Default.Add,
                        contentDescription = "Add Image"
                    )
                }
            }
        }
    ) { padding ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(padding)
                .padding(16.dp)
        ) {
            Text(
                text = "Cloudinary Image Upload",
                style = MaterialTheme.typography.headlineMedium,
                fontWeight = FontWeight.Bold,
                modifier = Modifier.padding(bottom = 16.dp)
            )

            if (uploadedImageUrls.isEmpty()) {
                // Empty state
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Icon(
                            imageVector = Icons.Default.Photo,
                            contentDescription = null,
                            modifier = Modifier.size(64.dp),
                            tint = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                        Spacer(modifier = Modifier.height(16.dp))
                        Text(
                            text = "No images uploaded yet",
                            style = MaterialTheme.typography.bodyLarge,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                        Spacer(modifier = Modifier.height(8.dp))
                        Text(
                            text = "Tap the + button to add your first image",
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                }
            } else {
                // Image grid
                LazyColumn(
                    verticalArrangement = Arrangement.spacedBy(16.dp)
                ) {
                    items(uploadedImageUrls) { imageUrl ->
                        Card(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable {
                                    // Could add full-screen view here
                                },
                            elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
                        ) {
                            Image(
                                painter = rememberAsyncImagePainter(imageUrl),
                                contentDescription = "Uploaded Image",
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .height(200.dp)
                                    .clip(RoundedCornerShape(8.dp)),
                                contentScale = ContentScale.Crop
                            )
                        }
                    }
                }
            }
        }
    }
}

// Helper function to copy URI content to a file
suspend fun copyUriToFile(context: Context, uri: Uri): File? {
    return withContext(Dispatchers.IO) {
        try {
            val inputStream: InputStream? = context.contentResolver.openInputStream(uri)
            val tempFile = File(context.getExternalFilesDir(null), "temp_image_${System.currentTimeMillis()}.jpg")

            inputStream?.use { input ->
                FileOutputStream(tempFile).use { output ->
                    input.copyTo(output)
                }
            }
            tempFile
        } catch (e: Exception) {
            e.printStackTrace()
            null
        }
    }
}

// Cloudinary upload function
suspend fun uploadImageToCloudinary(
    context: Context,
    imageFile: File,
    onStart: () -> Unit,
    onSuccess: (String) -> Unit,
    onError: (String) -> Unit
) {
    withContext(Dispatchers.IO) {
        try {
            onStart()

            // Cloudinary configuration - UPDATE THESE VALUES
            val cloudName = "dczvhue59"  // Replace with your actual cloud name
            val uploadPreset = "ml_default"  // Replace with your actual upload preset

            // Validate file exists and is readable
            if (!imageFile.exists() || !imageFile.canRead()) {
                withContext(Dispatchers.Main) {
                    onError("Image file not found or not readable")
                }
                return@withContext
            }

            // Check file size (Cloudinary free tier has limits)
            val fileSizeInMB = imageFile.length() / (1024 * 1024)
            if (fileSizeInMB > 10) {
                withContext(Dispatchers.Main) {
                    onError("File too large. Maximum size is 10MB")
                }
                return@withContext
            }

            println("📤 Uploading file: ${imageFile.name}, Size: ${fileSizeInMB}MB")

            val requestBody = MultipartBody.Builder()
                .setType(MultipartBody.FORM)
                .addFormDataPart(
                    "file",
                    imageFile.name,
                    imageFile.asRequestBody("image/jpeg".toMediaTypeOrNull())
                )
                .addFormDataPart("upload_preset", uploadPreset)
                .addFormDataPart("folder", "android_uploads") // Optional: organize uploads
                .build()

            val request = Request.Builder()
                .url("https://api.cloudinary.com/v1_1/$cloudName/image/upload")
                .post(requestBody)
                .addHeader("User-Agent", "AndroidApp/1.0")
                .build()

            println("🌐 Making request to: https://api.cloudinary.com/v1_1/$cloudName/image/upload")

            val client = OkHttpClient.Builder()
                .connectTimeout(30, java.util.concurrent.TimeUnit.SECONDS)
                .writeTimeout(60, java.util.concurrent.TimeUnit.SECONDS)
                .readTimeout(60, java.util.concurrent.TimeUnit.SECONDS)
                .build()

            val response = client.newCall(request).execute()
            val responseBody = response.body?.string()

            println("📥 Response code: ${response.code}")
            println("📥 Response body: $responseBody")

            if (response.isSuccessful && responseBody != null) {
                try {
                    val json = JSONObject(responseBody)
                    val imageUrl = json.getString("secure_url")
                    println("✅ Upload successful: $imageUrl")
                    withContext(Dispatchers.Main) {
                        onSuccess(imageUrl)
                    }
                } catch (e: Exception) {
                    println("❌ Error parsing response: ${e.message}")
                    withContext(Dispatchers.Main) {
                        onError("Error parsing upload response: ${e.message}")
                    }
                }
            } else {
                val errorMessage = when (response.code) {
                    400 -> {
                        "Bad Request (400): Check your upload preset '$uploadPreset' exists and is configured correctly"
                    }
                    401 -> {
                        "Unauthorized (401): Check your cloud name '$cloudName' is correct"
                    }
                    403 -> {
                        "Forbidden (403): Upload preset may not allow unsigned uploads"
                    }
                    413 -> {
                        "File too large (413): Reduce image size"
                    }
                    else -> {
                        "Upload failed (${response.code}): ${responseBody ?: "Unknown error"}"
                    }
                }
                println("❌ Upload failed: $errorMessage")
                withContext(Dispatchers.Main) {
                    onError(errorMessage)
                }
            }
        } catch (e: Exception) {
            println("❌ Exception during upload: ${e.message}")
            e.printStackTrace()
            withContext(Dispatchers.Main) {
                onError("Upload failed: ${e.localizedMessage ?: e.message ?: "Unknown error"}")
            }
        }
    }
}
