  Manifest android  CAMERA android.Manifest.permission  Activity android.app  ImageUploadApp android.app.Activity  MyFirstAppTheme android.app.Activity  onCreate android.app.Activity  
setContent android.app.Activity  ContentResolver android.content  Context android.content  openInputStream android.content.ContentResolver  ImageUploadApp android.content.Context  MyFirstAppTheme android.content.Context  contentResolver android.content.Context  getExternalFilesDir android.content.Context  packageName android.content.Context  
setContent android.content.Context  ImageUploadApp android.content.ContextWrapper  MyFirstAppTheme android.content.ContextWrapper  
setContent android.content.ContextWrapper  Uri android.net  let android.net.Uri  Build 
android.os  Bundle 
android.os  SDK_INT android.os.Build.VERSION  S android.os.Build.VERSION_CODES  ImageUploadApp  android.view.ContextThemeWrapper  MyFirstAppTheme  android.view.ContextThemeWrapper  
setContent  android.view.ContextThemeWrapper  ComponentActivity androidx.activity  Bundle #androidx.activity.ComponentActivity  	Companion #androidx.activity.ComponentActivity  ImageUploadApp #androidx.activity.ComponentActivity  MyFirstAppTheme #androidx.activity.ComponentActivity  onCreate #androidx.activity.ComponentActivity  
setContent #androidx.activity.ComponentActivity  ImageUploadApp -androidx.activity.ComponentActivity.Companion  MyFirstAppTheme -androidx.activity.ComponentActivity.Companion  
setContent -androidx.activity.ComponentActivity.Companion  ManagedActivityResultLauncher androidx.activity.compose  !rememberLauncherForActivityResult androidx.activity.compose  
setContent androidx.activity.compose  launch 7androidx.activity.compose.ManagedActivityResultLauncher  ActivityResultContracts !androidx.activity.result.contract  
GetContent 9androidx.activity.result.contract.ActivityResultContracts  RequestPermission 9androidx.activity.result.contract.ActivityResultContracts  TakePicture 9androidx.activity.result.contract.ActivityResultContracts  Image androidx.compose.foundation  	clickable androidx.compose.foundation  isSystemInDarkTheme androidx.compose.foundation  ActivityResultContracts "androidx.compose.foundation.layout  	Alignment "androidx.compose.foundation.layout  Arrangement "androidx.compose.foundation.layout  Box "androidx.compose.foundation.layout  BoxScope "androidx.compose.foundation.layout  Bundle "androidx.compose.foundation.layout  Card "androidx.compose.foundation.layout  CardDefaults "androidx.compose.foundation.layout  CircularProgressIndicator "androidx.compose.foundation.layout  Column "androidx.compose.foundation.layout  ColumnScope "androidx.compose.foundation.layout  ComponentActivity "androidx.compose.foundation.layout  
Composable "androidx.compose.foundation.layout  ContentScale "androidx.compose.foundation.layout  Context "androidx.compose.foundation.layout  Dispatchers "androidx.compose.foundation.layout  	Exception "androidx.compose.foundation.layout  ExperimentalMaterial3Api "androidx.compose.foundation.layout  File "androidx.compose.foundation.layout  FileOutputStream "androidx.compose.foundation.layout  FileProvider "androidx.compose.foundation.layout  FloatingActionButton "androidx.compose.foundation.layout  
FontWeight "androidx.compose.foundation.layout  Icon "androidx.compose.foundation.layout  Icons "androidx.compose.foundation.layout  Image "androidx.compose.foundation.layout  ImageUploadApp "androidx.compose.foundation.layout  InputStream "androidx.compose.foundation.layout  
JSONObject "androidx.compose.foundation.layout  
LazyColumn "androidx.compose.foundation.layout  Manifest "androidx.compose.foundation.layout  
MaterialTheme "androidx.compose.foundation.layout  ModalBottomSheet "androidx.compose.foundation.layout  Modifier "androidx.compose.foundation.layout  
MultipartBody "androidx.compose.foundation.layout  MyFirstAppTheme "androidx.compose.foundation.layout  OkHttpClient "androidx.compose.foundation.layout  OptIn "androidx.compose.foundation.layout  
PaddingValues "androidx.compose.foundation.layout  Request "androidx.compose.foundation.layout  RoundedCornerShape "androidx.compose.foundation.layout  Row "androidx.compose.foundation.layout  RowScope "androidx.compose.foundation.layout  Scaffold "androidx.compose.foundation.layout  SnackbarHost "androidx.compose.foundation.layout  SnackbarHostState "androidx.compose.foundation.layout  Spacer "androidx.compose.foundation.layout  String "androidx.compose.foundation.layout  System "androidx.compose.foundation.layout  Text "androidx.compose.foundation.layout  Unit "androidx.compose.foundation.layout  Uri "androidx.compose.foundation.layout  
asRequestBody "androidx.compose.foundation.layout  
cardElevation "androidx.compose.foundation.layout  	clickable "androidx.compose.foundation.layout  clip "androidx.compose.foundation.layout  copyTo "androidx.compose.foundation.layout  
copyUriToFile "androidx.compose.foundation.layout  fillMaxSize "androidx.compose.foundation.layout  fillMaxWidth "androidx.compose.foundation.layout  getValue "androidx.compose.foundation.layout  height "androidx.compose.foundation.layout  java "androidx.compose.foundation.layout  launch "androidx.compose.foundation.layout  let "androidx.compose.foundation.layout  listOf "androidx.compose.foundation.layout  mutableStateOf "androidx.compose.foundation.layout  padding "androidx.compose.foundation.layout  plus "androidx.compose.foundation.layout  println "androidx.compose.foundation.layout  provideDelegate "androidx.compose.foundation.layout  remember "androidx.compose.foundation.layout  rememberAsyncImagePainter "androidx.compose.foundation.layout  rememberCoroutineScope "androidx.compose.foundation.layout  setValue "androidx.compose.foundation.layout  size "androidx.compose.foundation.layout  spacedBy "androidx.compose.foundation.layout  toMediaTypeOrNull "androidx.compose.foundation.layout  uploadImageToCloudinary "androidx.compose.foundation.layout  use "androidx.compose.foundation.layout  withContext "androidx.compose.foundation.layout  
Horizontal .androidx.compose.foundation.layout.Arrangement  HorizontalOrVertical .androidx.compose.foundation.layout.Arrangement  Vertical .androidx.compose.foundation.layout.Arrangement  spacedBy .androidx.compose.foundation.layout.Arrangement  	Alignment +androidx.compose.foundation.layout.BoxScope  Column +androidx.compose.foundation.layout.BoxScope  Icon +androidx.compose.foundation.layout.BoxScope  Icons +androidx.compose.foundation.layout.BoxScope  
MaterialTheme +androidx.compose.foundation.layout.BoxScope  Modifier +androidx.compose.foundation.layout.BoxScope  Photo +androidx.compose.foundation.layout.BoxScope  Spacer +androidx.compose.foundation.layout.BoxScope  Text +androidx.compose.foundation.layout.BoxScope  dp +androidx.compose.foundation.layout.BoxScope  height +androidx.compose.foundation.layout.BoxScope  size +androidx.compose.foundation.layout.BoxScope  	Alignment .androidx.compose.foundation.layout.ColumnScope  Arrangement .androidx.compose.foundation.layout.ColumnScope  Box .androidx.compose.foundation.layout.ColumnScope  Camera .androidx.compose.foundation.layout.ColumnScope  Card .androidx.compose.foundation.layout.ColumnScope  CardDefaults .androidx.compose.foundation.layout.ColumnScope  Column .androidx.compose.foundation.layout.ColumnScope  ContentScale .androidx.compose.foundation.layout.ColumnScope  
FontWeight .androidx.compose.foundation.layout.ColumnScope  Icon .androidx.compose.foundation.layout.ColumnScope  Icons .androidx.compose.foundation.layout.ColumnScope  Image .androidx.compose.foundation.layout.ColumnScope  
LazyColumn .androidx.compose.foundation.layout.ColumnScope  Manifest .androidx.compose.foundation.layout.ColumnScope  
MaterialTheme .androidx.compose.foundation.layout.ColumnScope  Modifier .androidx.compose.foundation.layout.ColumnScope  Photo .androidx.compose.foundation.layout.ColumnScope  RoundedCornerShape .androidx.compose.foundation.layout.ColumnScope  Row .androidx.compose.foundation.layout.ColumnScope  Spacer .androidx.compose.foundation.layout.ColumnScope  Text .androidx.compose.foundation.layout.ColumnScope  
cardElevation .androidx.compose.foundation.layout.ColumnScope  	clickable .androidx.compose.foundation.layout.ColumnScope  clip .androidx.compose.foundation.layout.ColumnScope  dp .androidx.compose.foundation.layout.ColumnScope  fillMaxSize .androidx.compose.foundation.layout.ColumnScope  fillMaxWidth .androidx.compose.foundation.layout.ColumnScope  height .androidx.compose.foundation.layout.ColumnScope  items .androidx.compose.foundation.layout.ColumnScope  padding .androidx.compose.foundation.layout.ColumnScope  rememberAsyncImagePainter .androidx.compose.foundation.layout.ColumnScope  size .androidx.compose.foundation.layout.ColumnScope  spacedBy .androidx.compose.foundation.layout.ColumnScope  Camera +androidx.compose.foundation.layout.RowScope  Icon +androidx.compose.foundation.layout.RowScope  Icons +androidx.compose.foundation.layout.RowScope  
MaterialTheme +androidx.compose.foundation.layout.RowScope  Modifier +androidx.compose.foundation.layout.RowScope  Photo +androidx.compose.foundation.layout.RowScope  Text +androidx.compose.foundation.layout.RowScope  dp +androidx.compose.foundation.layout.RowScope  padding +androidx.compose.foundation.layout.RowScope  
LazyColumn  androidx.compose.foundation.lazy  
LazyItemScope  androidx.compose.foundation.lazy  
LazyListScope  androidx.compose.foundation.lazy  items  androidx.compose.foundation.lazy  Card .androidx.compose.foundation.lazy.LazyItemScope  CardDefaults .androidx.compose.foundation.lazy.LazyItemScope  ContentScale .androidx.compose.foundation.lazy.LazyItemScope  Image .androidx.compose.foundation.lazy.LazyItemScope  Modifier .androidx.compose.foundation.lazy.LazyItemScope  RoundedCornerShape .androidx.compose.foundation.lazy.LazyItemScope  
cardElevation .androidx.compose.foundation.lazy.LazyItemScope  	clickable .androidx.compose.foundation.lazy.LazyItemScope  clip .androidx.compose.foundation.lazy.LazyItemScope  dp .androidx.compose.foundation.lazy.LazyItemScope  fillMaxWidth .androidx.compose.foundation.lazy.LazyItemScope  height .androidx.compose.foundation.lazy.LazyItemScope  rememberAsyncImagePainter .androidx.compose.foundation.lazy.LazyItemScope  Card .androidx.compose.foundation.lazy.LazyListScope  CardDefaults .androidx.compose.foundation.lazy.LazyListScope  ContentScale .androidx.compose.foundation.lazy.LazyListScope  Image .androidx.compose.foundation.lazy.LazyListScope  Modifier .androidx.compose.foundation.lazy.LazyListScope  RoundedCornerShape .androidx.compose.foundation.lazy.LazyListScope  
cardElevation .androidx.compose.foundation.lazy.LazyListScope  	clickable .androidx.compose.foundation.lazy.LazyListScope  clip .androidx.compose.foundation.lazy.LazyListScope  dp .androidx.compose.foundation.lazy.LazyListScope  fillMaxWidth .androidx.compose.foundation.lazy.LazyListScope  height .androidx.compose.foundation.lazy.LazyListScope  items .androidx.compose.foundation.lazy.LazyListScope  rememberAsyncImagePainter .androidx.compose.foundation.lazy.LazyListScope  RoundedCornerShape !androidx.compose.foundation.shape  Icons androidx.compose.material.icons  Default %androidx.compose.material.icons.Icons  Filled %androidx.compose.material.icons.Icons  Add ,androidx.compose.material.icons.Icons.Filled  Camera ,androidx.compose.material.icons.Icons.Filled  Photo ,androidx.compose.material.icons.Icons.Filled  Add &androidx.compose.material.icons.filled  Camera &androidx.compose.material.icons.filled  Photo &androidx.compose.material.icons.filled  ActivityResultContracts androidx.compose.material3  	Alignment androidx.compose.material3  Arrangement androidx.compose.material3  Box androidx.compose.material3  Bundle androidx.compose.material3  Card androidx.compose.material3  CardDefaults androidx.compose.material3  
CardElevation androidx.compose.material3  CircularProgressIndicator androidx.compose.material3  ColorScheme androidx.compose.material3  Column androidx.compose.material3  ComponentActivity androidx.compose.material3  
Composable androidx.compose.material3  ContentScale androidx.compose.material3  Context androidx.compose.material3  Dispatchers androidx.compose.material3  	Exception androidx.compose.material3  ExperimentalMaterial3Api androidx.compose.material3  File androidx.compose.material3  FileOutputStream androidx.compose.material3  FileProvider androidx.compose.material3  FloatingActionButton androidx.compose.material3  
FontWeight androidx.compose.material3  Icon androidx.compose.material3  Icons androidx.compose.material3  Image androidx.compose.material3  ImageUploadApp androidx.compose.material3  InputStream androidx.compose.material3  
JSONObject androidx.compose.material3  
LazyColumn androidx.compose.material3  Manifest androidx.compose.material3  
MaterialTheme androidx.compose.material3  ModalBottomSheet androidx.compose.material3  Modifier androidx.compose.material3  
MultipartBody androidx.compose.material3  MyFirstAppTheme androidx.compose.material3  OkHttpClient androidx.compose.material3  OptIn androidx.compose.material3  Request androidx.compose.material3  RoundedCornerShape androidx.compose.material3  Row androidx.compose.material3  Scaffold androidx.compose.material3  SnackbarHost androidx.compose.material3  SnackbarHostState androidx.compose.material3  SnackbarResult androidx.compose.material3  Spacer androidx.compose.material3  String androidx.compose.material3  System androidx.compose.material3  Text androidx.compose.material3  
Typography androidx.compose.material3  Unit androidx.compose.material3  Uri androidx.compose.material3  
asRequestBody androidx.compose.material3  
cardElevation androidx.compose.material3  	clickable androidx.compose.material3  clip androidx.compose.material3  copyTo androidx.compose.material3  
copyUriToFile androidx.compose.material3  darkColorScheme androidx.compose.material3  dynamicDarkColorScheme androidx.compose.material3  dynamicLightColorScheme androidx.compose.material3  fillMaxSize androidx.compose.material3  fillMaxWidth androidx.compose.material3  getValue androidx.compose.material3  height androidx.compose.material3  java androidx.compose.material3  launch androidx.compose.material3  let androidx.compose.material3  lightColorScheme androidx.compose.material3  listOf androidx.compose.material3  mutableStateOf androidx.compose.material3  padding androidx.compose.material3  plus androidx.compose.material3  println androidx.compose.material3  provideDelegate androidx.compose.material3  remember androidx.compose.material3  rememberAsyncImagePainter androidx.compose.material3  rememberCoroutineScope androidx.compose.material3  setValue androidx.compose.material3  size androidx.compose.material3  spacedBy androidx.compose.material3  toMediaTypeOrNull androidx.compose.material3  uploadImageToCloudinary androidx.compose.material3  use androidx.compose.material3  withContext androidx.compose.material3  
cardElevation 'androidx.compose.material3.CardDefaults  	onPrimary &androidx.compose.material3.ColorScheme  onSurfaceVariant &androidx.compose.material3.ColorScheme  colorScheme (androidx.compose.material3.MaterialTheme  
typography (androidx.compose.material3.MaterialTheme  showSnackbar ,androidx.compose.material3.SnackbarHostState  	bodyLarge %androidx.compose.material3.Typography  
bodyMedium %androidx.compose.material3.Typography  headlineMedium %androidx.compose.material3.Typography  
headlineSmall %androidx.compose.material3.Typography  ActivityResultContracts androidx.compose.runtime  	Alignment androidx.compose.runtime  Arrangement androidx.compose.runtime  Box androidx.compose.runtime  Bundle androidx.compose.runtime  Card androidx.compose.runtime  CardDefaults androidx.compose.runtime  CircularProgressIndicator androidx.compose.runtime  Column androidx.compose.runtime  ComponentActivity androidx.compose.runtime  
Composable androidx.compose.runtime  ContentScale androidx.compose.runtime  Context androidx.compose.runtime  Dispatchers androidx.compose.runtime  	Exception androidx.compose.runtime  ExperimentalMaterial3Api androidx.compose.runtime  File androidx.compose.runtime  FileOutputStream androidx.compose.runtime  FileProvider androidx.compose.runtime  FloatingActionButton androidx.compose.runtime  
FontWeight androidx.compose.runtime  Icon androidx.compose.runtime  Icons androidx.compose.runtime  Image androidx.compose.runtime  ImageUploadApp androidx.compose.runtime  InputStream androidx.compose.runtime  
JSONObject androidx.compose.runtime  
LazyColumn androidx.compose.runtime  Manifest androidx.compose.runtime  
MaterialTheme androidx.compose.runtime  ModalBottomSheet androidx.compose.runtime  Modifier androidx.compose.runtime  
MultipartBody androidx.compose.runtime  MutableState androidx.compose.runtime  MyFirstAppTheme androidx.compose.runtime  OkHttpClient androidx.compose.runtime  OptIn androidx.compose.runtime  ProvidableCompositionLocal androidx.compose.runtime  Request androidx.compose.runtime  RoundedCornerShape androidx.compose.runtime  Row androidx.compose.runtime  Scaffold androidx.compose.runtime  SnackbarHost androidx.compose.runtime  SnackbarHostState androidx.compose.runtime  Spacer androidx.compose.runtime  String androidx.compose.runtime  System androidx.compose.runtime  Text androidx.compose.runtime  Unit androidx.compose.runtime  Uri androidx.compose.runtime  
asRequestBody androidx.compose.runtime  
cardElevation androidx.compose.runtime  	clickable androidx.compose.runtime  clip androidx.compose.runtime  copyTo androidx.compose.runtime  
copyUriToFile androidx.compose.runtime  fillMaxSize androidx.compose.runtime  fillMaxWidth androidx.compose.runtime  getValue androidx.compose.runtime  height androidx.compose.runtime  java androidx.compose.runtime  launch androidx.compose.runtime  let androidx.compose.runtime  listOf androidx.compose.runtime  mutableStateOf androidx.compose.runtime  padding androidx.compose.runtime  plus androidx.compose.runtime  println androidx.compose.runtime  provideDelegate androidx.compose.runtime  remember androidx.compose.runtime  rememberAsyncImagePainter androidx.compose.runtime  rememberCoroutineScope androidx.compose.runtime  setValue androidx.compose.runtime  size androidx.compose.runtime  spacedBy androidx.compose.runtime  toMediaTypeOrNull androidx.compose.runtime  uploadImageToCloudinary androidx.compose.runtime  use androidx.compose.runtime  withContext androidx.compose.runtime  
getCurrent )androidx.compose.runtime.CompositionLocal  setValue %androidx.compose.runtime.MutableState  current 3androidx.compose.runtime.ProvidableCompositionLocal  ComposableFunction0 !androidx.compose.runtime.internal  ComposableFunction1 !androidx.compose.runtime.internal  ComposableFunction2 !androidx.compose.runtime.internal  	Alignment androidx.compose.ui  Modifier androidx.compose.ui  Center androidx.compose.ui.Alignment  CenterHorizontally androidx.compose.ui.Alignment  CenterVertically androidx.compose.ui.Alignment  	Companion androidx.compose.ui.Alignment  
Horizontal androidx.compose.ui.Alignment  Vertical androidx.compose.ui.Alignment  Center 'androidx.compose.ui.Alignment.Companion  CenterHorizontally 'androidx.compose.ui.Alignment.Companion  CenterVertically 'androidx.compose.ui.Alignment.Companion  	Companion androidx.compose.ui.Modifier  	clickable androidx.compose.ui.Modifier  clip androidx.compose.ui.Modifier  fillMaxSize androidx.compose.ui.Modifier  fillMaxWidth androidx.compose.ui.Modifier  height androidx.compose.ui.Modifier  padding androidx.compose.ui.Modifier  size androidx.compose.ui.Modifier  fillMaxSize &androidx.compose.ui.Modifier.Companion  fillMaxWidth &androidx.compose.ui.Modifier.Companion  height &androidx.compose.ui.Modifier.Companion  padding &androidx.compose.ui.Modifier.Companion  size &androidx.compose.ui.Modifier.Companion  clip androidx.compose.ui.draw  Color androidx.compose.ui.graphics  ImageVector #androidx.compose.ui.graphics.vector  ContentScale androidx.compose.ui.layout  	Companion 'androidx.compose.ui.layout.ContentScale  Crop 'androidx.compose.ui.layout.ContentScale  Crop 1androidx.compose.ui.layout.ContentScale.Companion  LocalContext androidx.compose.ui.platform  	TextStyle androidx.compose.ui.text  
FontFamily androidx.compose.ui.text.font  
FontWeight androidx.compose.ui.text.font  SystemFontFamily androidx.compose.ui.text.font  	Companion (androidx.compose.ui.text.font.FontFamily  Default (androidx.compose.ui.text.font.FontFamily  Default 2androidx.compose.ui.text.font.FontFamily.Companion  Bold (androidx.compose.ui.text.font.FontWeight  	Companion (androidx.compose.ui.text.font.FontWeight  Normal (androidx.compose.ui.text.font.FontWeight  Bold 2androidx.compose.ui.text.font.FontWeight.Companion  Normal 2androidx.compose.ui.text.font.FontWeight.Companion  Dp androidx.compose.ui.unit  TextUnit androidx.compose.ui.unit  dp androidx.compose.ui.unit  getDp androidx.compose.ui.unit  sp androidx.compose.ui.unit  Bundle #androidx.core.app.ComponentActivity  ImageUploadApp #androidx.core.app.ComponentActivity  MyFirstAppTheme #androidx.core.app.ComponentActivity  
setContent #androidx.core.app.ComponentActivity  FileProvider androidx.core.content  
getUriForFile "androidx.core.content.FileProvider  AsyncImagePainter coil.compose  rememberAsyncImagePainter coil.compose  ActivityResultContracts com.example.myfirstapp  	Alignment com.example.myfirstapp  Arrangement com.example.myfirstapp  Box com.example.myfirstapp  Bundle com.example.myfirstapp  Card com.example.myfirstapp  CardDefaults com.example.myfirstapp  CircularProgressIndicator com.example.myfirstapp  Column com.example.myfirstapp  ComponentActivity com.example.myfirstapp  
Composable com.example.myfirstapp  ContentScale com.example.myfirstapp  Context com.example.myfirstapp  Dispatchers com.example.myfirstapp  	Exception com.example.myfirstapp  ExperimentalMaterial3Api com.example.myfirstapp  File com.example.myfirstapp  FileOutputStream com.example.myfirstapp  FileProvider com.example.myfirstapp  FloatingActionButton com.example.myfirstapp  
FontWeight com.example.myfirstapp  Icon com.example.myfirstapp  Icons com.example.myfirstapp  Image com.example.myfirstapp  ImageUploadApp com.example.myfirstapp  InputStream com.example.myfirstapp  
JSONObject com.example.myfirstapp  
LazyColumn com.example.myfirstapp  MainActivity com.example.myfirstapp  Manifest com.example.myfirstapp  
MaterialTheme com.example.myfirstapp  ModalBottomSheet com.example.myfirstapp  Modifier com.example.myfirstapp  
MultipartBody com.example.myfirstapp  MyFirstAppTheme com.example.myfirstapp  OkHttpClient com.example.myfirstapp  OptIn com.example.myfirstapp  Request com.example.myfirstapp  RoundedCornerShape com.example.myfirstapp  Row com.example.myfirstapp  Scaffold com.example.myfirstapp  SnackbarHost com.example.myfirstapp  SnackbarHostState com.example.myfirstapp  Spacer com.example.myfirstapp  String com.example.myfirstapp  System com.example.myfirstapp  Text com.example.myfirstapp  Unit com.example.myfirstapp  Uri com.example.myfirstapp  
asRequestBody com.example.myfirstapp  
cardElevation com.example.myfirstapp  	clickable com.example.myfirstapp  clip com.example.myfirstapp  copyTo com.example.myfirstapp  
copyUriToFile com.example.myfirstapp  fillMaxSize com.example.myfirstapp  fillMaxWidth com.example.myfirstapp  getValue com.example.myfirstapp  height com.example.myfirstapp  java com.example.myfirstapp  launch com.example.myfirstapp  let com.example.myfirstapp  listOf com.example.myfirstapp  mutableStateOf com.example.myfirstapp  padding com.example.myfirstapp  plus com.example.myfirstapp  println com.example.myfirstapp  provideDelegate com.example.myfirstapp  remember com.example.myfirstapp  rememberAsyncImagePainter com.example.myfirstapp  rememberCoroutineScope com.example.myfirstapp  setValue com.example.myfirstapp  size com.example.myfirstapp  spacedBy com.example.myfirstapp  toMediaTypeOrNull com.example.myfirstapp  uploadImageToCloudinary com.example.myfirstapp  use com.example.myfirstapp  withContext com.example.myfirstapp  ImageUploadApp #com.example.myfirstapp.MainActivity  MyFirstAppTheme #com.example.myfirstapp.MainActivity  
setContent #com.example.myfirstapp.MainActivity  Boolean com.example.myfirstapp.ui.theme  Build com.example.myfirstapp.ui.theme  
Composable com.example.myfirstapp.ui.theme  DarkColorScheme com.example.myfirstapp.ui.theme  
FontFamily com.example.myfirstapp.ui.theme  
FontWeight com.example.myfirstapp.ui.theme  LightColorScheme com.example.myfirstapp.ui.theme  MyFirstAppTheme com.example.myfirstapp.ui.theme  Pink40 com.example.myfirstapp.ui.theme  Pink80 com.example.myfirstapp.ui.theme  Purple40 com.example.myfirstapp.ui.theme  Purple80 com.example.myfirstapp.ui.theme  PurpleGrey40 com.example.myfirstapp.ui.theme  PurpleGrey80 com.example.myfirstapp.ui.theme  
Typography com.example.myfirstapp.ui.theme  Unit com.example.myfirstapp.ui.theme  File java.io  FileOutputStream java.io  IOException java.io  InputStream java.io  
asRequestBody java.io.File  canRead java.io.File  exists java.io.File  length java.io.File  name java.io.File  use java.io.FileOutputStream  copyTo java.io.InputStream  use java.io.InputStream  Class 	java.lang  currentTimeMillis java.lang.System  
BigDecimal 	java.math  
BigInteger 	java.math  TimeUnit java.util.concurrent  SECONDS java.util.concurrent.TimeUnit  Array kotlin  BooleanArray kotlin  	ByteArray kotlin  	CharArray kotlin  DoubleArray kotlin  	Exception kotlin  
FloatArray kotlin  	Function0 kotlin  	Function1 kotlin  IntArray kotlin  	LongArray kotlin  Nothing kotlin  OptIn kotlin  
ShortArray kotlin  
UByteArray kotlin  	UIntArray kotlin  
ULongArray kotlin  UShortArray kotlin  let kotlin  plus kotlin  use kotlin  not kotlin.Boolean  sp 
kotlin.Double  localizedMessage kotlin.Exception  message kotlin.Exception  printStackTrace kotlin.Exception  invoke kotlin.Function0  invoke kotlin.Function1  	compareTo 
kotlin.Int  times 
kotlin.Int  	compareTo kotlin.Long  div kotlin.Long  toMediaTypeOrNull 
kotlin.String  localizedMessage kotlin.Throwable  message kotlin.Throwable  printStackTrace kotlin.Throwable  List kotlin.collections  Map kotlin.collections  Set kotlin.collections  listOf kotlin.collections  plus kotlin.collections  isEmpty kotlin.collections.List  plus kotlin.collections.List  SuspendFunction1 kotlin.coroutines  copyTo 	kotlin.io  println 	kotlin.io  use 	kotlin.io  java 
kotlin.jvm  KMutableProperty0 kotlin.reflect  Sequence kotlin.sequences  plus kotlin.sequences  plus kotlin.text  CoroutineDispatcher kotlinx.coroutines  CoroutineScope kotlinx.coroutines  Dispatchers kotlinx.coroutines  Job kotlinx.coroutines  MainCoroutineDispatcher kotlinx.coroutines  launch kotlinx.coroutines  withContext kotlinx.coroutines  Dispatchers !kotlinx.coroutines.CoroutineScope  File !kotlinx.coroutines.CoroutineScope  FileOutputStream !kotlinx.coroutines.CoroutineScope  
JSONObject !kotlinx.coroutines.CoroutineScope  
MultipartBody !kotlinx.coroutines.CoroutineScope  OkHttpClient !kotlinx.coroutines.CoroutineScope  Request !kotlinx.coroutines.CoroutineScope  System !kotlinx.coroutines.CoroutineScope  
asRequestBody !kotlinx.coroutines.CoroutineScope  copyTo !kotlinx.coroutines.CoroutineScope  
copyUriToFile !kotlinx.coroutines.CoroutineScope  java !kotlinx.coroutines.CoroutineScope  launch !kotlinx.coroutines.CoroutineScope  plus !kotlinx.coroutines.CoroutineScope  println !kotlinx.coroutines.CoroutineScope  toMediaTypeOrNull !kotlinx.coroutines.CoroutineScope  uploadImageToCloudinary !kotlinx.coroutines.CoroutineScope  use !kotlinx.coroutines.CoroutineScope  withContext !kotlinx.coroutines.CoroutineScope  IO kotlinx.coroutines.Dispatchers  Main kotlinx.coroutines.Dispatchers  ActivityResultContracts okhttp3  	Alignment okhttp3  Arrangement okhttp3  Box okhttp3  Bundle okhttp3  Call okhttp3  Card okhttp3  CardDefaults okhttp3  CircularProgressIndicator okhttp3  Column okhttp3  ComponentActivity okhttp3  
Composable okhttp3  ContentScale okhttp3  Context okhttp3  Dispatchers okhttp3  	Exception okhttp3  ExperimentalMaterial3Api okhttp3  File okhttp3  FileOutputStream okhttp3  FileProvider okhttp3  FloatingActionButton okhttp3  
FontWeight okhttp3  Icon okhttp3  Icons okhttp3  Image okhttp3  ImageUploadApp okhttp3  InputStream okhttp3  
JSONObject okhttp3  
LazyColumn okhttp3  Manifest okhttp3  
MaterialTheme okhttp3  	MediaType okhttp3  ModalBottomSheet okhttp3  Modifier okhttp3  
MultipartBody okhttp3  MyFirstAppTheme okhttp3  OkHttpClient okhttp3  OptIn okhttp3  Request okhttp3  RequestBody okhttp3  Response okhttp3  ResponseBody okhttp3  RoundedCornerShape okhttp3  Row okhttp3  Scaffold okhttp3  SnackbarHost okhttp3  SnackbarHostState okhttp3  Spacer okhttp3  String okhttp3  System okhttp3  Text okhttp3  Unit okhttp3  Uri okhttp3  
asRequestBody okhttp3  
cardElevation okhttp3  	clickable okhttp3  clip okhttp3  copyTo okhttp3  
copyUriToFile okhttp3  fillMaxSize okhttp3  fillMaxWidth okhttp3  getValue okhttp3  height okhttp3  java okhttp3  launch okhttp3  let okhttp3  listOf okhttp3  mutableStateOf okhttp3  padding okhttp3  plus okhttp3  println okhttp3  provideDelegate okhttp3  remember okhttp3  rememberAsyncImagePainter okhttp3  rememberCoroutineScope okhttp3  setValue okhttp3  size okhttp3  spacedBy okhttp3  toMediaTypeOrNull okhttp3  uploadImageToCloudinary okhttp3  use okhttp3  withContext okhttp3  execute okhttp3.Call  toMediaTypeOrNull okhttp3.MediaType.Companion  Builder okhttp3.MultipartBody  	Companion okhttp3.MultipartBody  FORM okhttp3.MultipartBody  addFormDataPart okhttp3.MultipartBody.Builder  build okhttp3.MultipartBody.Builder  setType okhttp3.MultipartBody.Builder  FORM okhttp3.MultipartBody.Companion  Builder okhttp3.OkHttpClient  	Companion okhttp3.OkHttpClient  newCall okhttp3.OkHttpClient  build okhttp3.OkHttpClient.Builder  connectTimeout okhttp3.OkHttpClient.Builder  readTimeout okhttp3.OkHttpClient.Builder  writeTimeout okhttp3.OkHttpClient.Builder  Builder okhttp3.Request  	addHeader okhttp3.Request.Builder  build okhttp3.Request.Builder  post okhttp3.Request.Builder  url okhttp3.Request.Builder  
asRequestBody okhttp3.RequestBody.Companion  body okhttp3.Response  code okhttp3.Response  isSuccessful okhttp3.Response  string okhttp3.ResponseBody  
JSONObject org.json  	getString org.json.JSONObject  POST_NOTIFICATIONS android.Manifest.permission  ic_menu_camera android.R.drawable  ic_menu_close_clear_cancel android.R.drawable  Notification android.app  NotificationChannel android.app  NotificationManager android.app  
PendingIntent android.app  Service android.app  apply android.app.NotificationChannel  description android.app.NotificationChannel  setShowBadge android.app.NotificationChannel  IMPORTANCE_LOW android.app.NotificationManager  createNotificationChannel android.app.NotificationManager  notify android.app.NotificationManager  FLAG_IMMUTABLE android.app.PendingIntent  FLAG_UPDATE_CURRENT android.app.PendingIntent  getActivity android.app.PendingIntent  
getService android.app.PendingIntent  ACTION_START_SERVICE android.app.Service  ACTION_STOP_SERVICE android.app.Service  ActivityCompat android.app.Service  BackgroundCameraService android.app.Service  Build android.app.Service  	ByteArray android.app.Service  CAPTURE_INTERVAL android.app.Service  
CHANNEL_ID android.app.Service  CameraCaptureSession android.app.Service  CameraCharacteristics android.app.Service  CameraDevice android.app.Service  
CameraManager android.app.Service  CaptureRequest android.app.Service  Context android.app.Service  CoroutineScope android.app.Service  Dispatchers android.app.Service  	Exception android.app.Service  File android.app.Service  FileOutputStream android.app.Service  Handler android.app.Service  
HandlerThread android.app.Service  ImageFormat android.app.Service  ImageReader android.app.Service  Int android.app.Service  Intent android.app.Service  InterruptedException android.app.Service  Log android.app.Service  MainActivity android.app.Service  Manifest android.app.Service  NOTIFICATION_ID android.app.Service  NotificationChannel android.app.Service  NotificationCompat android.app.Service  NotificationManager android.app.Service  PackageManager android.app.Service  
PendingIntent android.app.Service  RuntimeException android.app.Service  START_STICKY android.app.Service  	Semaphore android.app.Service  
SupervisorJob android.app.Service  Surface android.app.Service  System android.app.Service  TAG android.app.Service  TimeUnit android.app.Service  also android.app.Service  android android.app.Service  apply android.app.Service  cameraDevice android.app.Service  cameraOpenCloseLock android.app.Service  cancel android.app.Service  captureSession android.app.Service  captureStillPicture android.app.Service  createCameraPreviewSession android.app.Service  delay android.app.Service  firstOrNull android.app.Service  isServiceRunning android.app.Service  java android.app.Service  launch android.app.Service  listOf android.app.Service  onCreate android.app.Service  	onDestroy android.app.Service  startForeground android.app.Service  stopForeground android.app.Service  stopSelf android.app.Service  updateNotification android.app.Service  uploadImageToCloudinary android.app.Service  use android.app.Service  
StateCallback (android.app.Service.CameraCaptureSession  
StateCallback  android.app.Service.CameraDevice  
ComponentName android.content  Intent android.content  ACTION_START_SERVICE android.content.Context  ACTION_STOP_SERVICE android.content.Context  ActivityCompat android.content.Context  BackgroundCameraService android.content.Context  Build android.content.Context  	ByteArray android.content.Context  CAMERA_SERVICE android.content.Context  CAPTURE_INTERVAL android.content.Context  
CHANNEL_ID android.content.Context  CameraCaptureSession android.content.Context  CameraCharacteristics android.content.Context  CameraDevice android.content.Context  
CameraManager android.content.Context  CaptureRequest android.content.Context  Context android.content.Context  CoroutineScope android.content.Context  Dispatchers android.content.Context  	Exception android.content.Context  File android.content.Context  FileOutputStream android.content.Context  Handler android.content.Context  
HandlerThread android.content.Context  ImageFormat android.content.Context  ImageReader android.content.Context  Int android.content.Context  Intent android.content.Context  InterruptedException android.content.Context  Log android.content.Context  MainActivity android.content.Context  Manifest android.content.Context  NOTIFICATION_ID android.content.Context  NOTIFICATION_SERVICE android.content.Context  NotificationChannel android.content.Context  NotificationCompat android.content.Context  NotificationManager android.content.Context  PackageManager android.content.Context  
PendingIntent android.content.Context  RuntimeException android.content.Context  START_STICKY android.content.Context  	Semaphore android.content.Context  
SupervisorJob android.content.Context  Surface android.content.Context  System android.content.Context  TAG android.content.Context  TimeUnit android.content.Context  also android.content.Context  android android.content.Context  apply android.content.Context  cameraDevice android.content.Context  cameraOpenCloseLock android.content.Context  cancel android.content.Context  captureSession android.content.Context  captureStillPicture android.content.Context  createCameraPreviewSession android.content.Context  delay android.content.Context  firstOrNull android.content.Context  getSystemService android.content.Context  isServiceRunning android.content.Context  java android.content.Context  launch android.content.Context  listOf android.content.Context  startForegroundService android.content.Context  startService android.content.Context  updateNotification android.content.Context  uploadImageToCloudinary android.content.Context  use android.content.Context  
StateCallback ,android.content.Context.CameraCaptureSession  
StateCallback $android.content.Context.CameraDevice  ACTION_START_SERVICE android.content.ContextWrapper  ACTION_STOP_SERVICE android.content.ContextWrapper  ActivityCompat android.content.ContextWrapper  BackgroundCameraService android.content.ContextWrapper  Build android.content.ContextWrapper  	ByteArray android.content.ContextWrapper  CAPTURE_INTERVAL android.content.ContextWrapper  
CHANNEL_ID android.content.ContextWrapper  CameraCaptureSession android.content.ContextWrapper  CameraCharacteristics android.content.ContextWrapper  CameraDevice android.content.ContextWrapper  
CameraManager android.content.ContextWrapper  CaptureRequest android.content.ContextWrapper  Context android.content.ContextWrapper  CoroutineScope android.content.ContextWrapper  Dispatchers android.content.ContextWrapper  	Exception android.content.ContextWrapper  File android.content.ContextWrapper  FileOutputStream android.content.ContextWrapper  Handler android.content.ContextWrapper  
HandlerThread android.content.ContextWrapper  ImageFormat android.content.ContextWrapper  ImageReader android.content.ContextWrapper  Int android.content.ContextWrapper  Intent android.content.ContextWrapper  InterruptedException android.content.ContextWrapper  Log android.content.ContextWrapper  MainActivity android.content.ContextWrapper  Manifest android.content.ContextWrapper  NOTIFICATION_ID android.content.ContextWrapper  NotificationChannel android.content.ContextWrapper  NotificationCompat android.content.ContextWrapper  NotificationManager android.content.ContextWrapper  PackageManager android.content.ContextWrapper  
PendingIntent android.content.ContextWrapper  RuntimeException android.content.ContextWrapper  START_STICKY android.content.ContextWrapper  	Semaphore android.content.ContextWrapper  
SupervisorJob android.content.ContextWrapper  Surface android.content.ContextWrapper  System android.content.ContextWrapper  TAG android.content.ContextWrapper  TimeUnit android.content.ContextWrapper  also android.content.ContextWrapper  android android.content.ContextWrapper  apply android.content.ContextWrapper  cameraDevice android.content.ContextWrapper  cameraOpenCloseLock android.content.ContextWrapper  cancel android.content.ContextWrapper  captureSession android.content.ContextWrapper  captureStillPicture android.content.ContextWrapper  createCameraPreviewSession android.content.ContextWrapper  delay android.content.ContextWrapper  firstOrNull android.content.ContextWrapper  getExternalFilesDir android.content.ContextWrapper  getSystemService android.content.ContextWrapper  isServiceRunning android.content.ContextWrapper  java android.content.ContextWrapper  launch android.content.ContextWrapper  listOf android.content.ContextWrapper  updateNotification android.content.ContextWrapper  uploadImageToCloudinary android.content.ContextWrapper  use android.content.ContextWrapper  
StateCallback 3android.content.ContextWrapper.CameraCaptureSession  
StateCallback +android.content.ContextWrapper.CameraDevice  ACTION_STOP_SERVICE android.content.Intent  BackgroundCameraService android.content.Intent  action android.content.Intent  apply android.content.Intent  PackageManager android.content.pm  PERMISSION_GRANTED !android.content.pm.PackageManager  ImageFormat android.graphics  JPEG android.graphics.ImageFormat  ACTION_START_SERVICE android.hardware.camera2  ACTION_STOP_SERVICE android.hardware.camera2  ActivityCompat android.hardware.camera2  BackgroundCameraService android.hardware.camera2  Boolean android.hardware.camera2  Build android.hardware.camera2  	ByteArray android.hardware.camera2  CAPTURE_INTERVAL android.hardware.camera2  
CHANNEL_ID android.hardware.camera2  CameraCaptureSession android.hardware.camera2  CameraCharacteristics android.hardware.camera2  CameraDevice android.hardware.camera2  
CameraManager android.hardware.camera2  CaptureRequest android.hardware.camera2  Context android.hardware.camera2  CoroutineScope android.hardware.camera2  Dispatchers android.hardware.camera2  	Exception android.hardware.camera2  File android.hardware.camera2  FileOutputStream android.hardware.camera2  Handler android.hardware.camera2  
HandlerThread android.hardware.camera2  IBinder android.hardware.camera2  ImageFormat android.hardware.camera2  ImageReader android.hardware.camera2  Int android.hardware.camera2  Intent android.hardware.camera2  InterruptedException android.hardware.camera2  Job android.hardware.camera2  Log android.hardware.camera2  MainActivity android.hardware.camera2  Manifest android.hardware.camera2  NOTIFICATION_ID android.hardware.camera2  Notification android.hardware.camera2  NotificationChannel android.hardware.camera2  NotificationCompat android.hardware.camera2  NotificationManager android.hardware.camera2  PackageManager android.hardware.camera2  
PendingIntent android.hardware.camera2  RuntimeException android.hardware.camera2  START_STICKY android.hardware.camera2  	Semaphore android.hardware.camera2  Service android.hardware.camera2  String android.hardware.camera2  
SupervisorJob android.hardware.camera2  Surface android.hardware.camera2  System android.hardware.camera2  TAG android.hardware.camera2  TimeUnit android.hardware.camera2  also android.hardware.camera2  android android.hardware.camera2  apply android.hardware.camera2  cameraDevice android.hardware.camera2  cameraOpenCloseLock android.hardware.camera2  cancel android.hardware.camera2  captureSession android.hardware.camera2  captureStillPicture android.hardware.camera2  createCameraPreviewSession android.hardware.camera2  delay android.hardware.camera2  firstOrNull android.hardware.camera2  isServiceRunning android.hardware.camera2  java android.hardware.camera2  launch android.hardware.camera2  listOf android.hardware.camera2  updateNotification android.hardware.camera2  uploadImageToCloudinary android.hardware.camera2  use android.hardware.camera2  
StateCallback -android.hardware.camera2.CameraCaptureSession  capture -android.hardware.camera2.CameraCaptureSession  close -android.hardware.camera2.CameraCaptureSession  Log ;android.hardware.camera2.CameraCaptureSession.StateCallback  TAG ;android.hardware.camera2.CameraCaptureSession.StateCallback  captureSession ;android.hardware.camera2.CameraCaptureSession.StateCallback  Key .android.hardware.camera2.CameraCharacteristics  LENS_FACING .android.hardware.camera2.CameraCharacteristics  LENS_FACING_BACK .android.hardware.camera2.CameraCharacteristics  get .android.hardware.camera2.CameraCharacteristics  
StateCallback %android.hardware.camera2.CameraDevice  TEMPLATE_STILL_CAPTURE %android.hardware.camera2.CameraDevice  close %android.hardware.camera2.CameraDevice  createCaptureRequest %android.hardware.camera2.CameraDevice  createCaptureSession %android.hardware.camera2.CameraDevice  Log 3android.hardware.camera2.CameraDevice.StateCallback  TAG 3android.hardware.camera2.CameraDevice.StateCallback  cameraDevice 3android.hardware.camera2.CameraDevice.StateCallback  cameraOpenCloseLock 3android.hardware.camera2.CameraDevice.StateCallback  createCameraPreviewSession 3android.hardware.camera2.CameraDevice.StateCallback  cameraIdList &android.hardware.camera2.CameraManager  getCameraCharacteristics &android.hardware.camera2.CameraManager  
openCamera &android.hardware.camera2.CameraManager  CONTROL_AE_MODE_ON_AUTO_FLASH 'android.hardware.camera2.CameraMetadata  "CONTROL_AF_MODE_CONTINUOUS_PICTURE 'android.hardware.camera2.CameraMetadata  LENS_FACING_BACK 'android.hardware.camera2.CameraMetadata  Builder 'android.hardware.camera2.CaptureRequest  CONTROL_AE_MODE 'android.hardware.camera2.CaptureRequest  CONTROL_AE_MODE_ON_AUTO_FLASH 'android.hardware.camera2.CaptureRequest  CONTROL_AF_MODE 'android.hardware.camera2.CaptureRequest  "CONTROL_AF_MODE_CONTINUOUS_PICTURE 'android.hardware.camera2.CaptureRequest  Key 'android.hardware.camera2.CaptureRequest  	addTarget /android.hardware.camera2.CaptureRequest.Builder  build /android.hardware.camera2.CaptureRequest.Builder  set /android.hardware.camera2.CaptureRequest.Builder  media  android.hardware.camera2.android  Image &android.hardware.camera2.android.media  Image 
android.media  ImageReader 
android.media  close android.media.Image  planes android.media.Image  buffer android.media.Image.Plane  OnImageAvailableListener android.media.ImageReader  acquireLatestImage android.media.ImageReader  close android.media.ImageReader  newInstance android.media.ImageReader  setOnImageAvailableListener android.media.ImageReader  surface android.media.ImageReader  Handler 
android.os  
HandlerThread 
android.os  IBinder 
android.os  Looper 
android.os  O android.os.Build.VERSION_CODES  TIRAMISU android.os.Build.VERSION_CODES  post android.os.Handler  also android.os.HandlerThread  join android.os.HandlerThread  looper android.os.HandlerThread  
quitSafely android.os.HandlerThread  start android.os.HandlerThread  Log android.util  Size android.util  d android.util.Log  e android.util.Log  Surface android.view  BackgroundCameraService "androidx.compose.foundation.layout  Build "androidx.compose.foundation.layout  Button "androidx.compose.foundation.layout  ButtonDefaults "androidx.compose.foundation.layout  apply "androidx.compose.foundation.layout  buttonColors "androidx.compose.foundation.layout  startBackgroundCameraService "androidx.compose.foundation.layout  stopBackgroundCameraService "androidx.compose.foundation.layout  weight "androidx.compose.foundation.layout  Build .androidx.compose.foundation.layout.ColumnScope  Button .androidx.compose.foundation.layout.ColumnScope  ButtonDefaults .androidx.compose.foundation.layout.ColumnScope  buttonColors .androidx.compose.foundation.layout.ColumnScope  launch .androidx.compose.foundation.layout.ColumnScope  startBackgroundCameraService .androidx.compose.foundation.layout.ColumnScope  stopBackgroundCameraService .androidx.compose.foundation.layout.ColumnScope  weight .androidx.compose.foundation.layout.ColumnScope  Build +androidx.compose.foundation.layout.RowScope  Button +androidx.compose.foundation.layout.RowScope  ButtonDefaults +androidx.compose.foundation.layout.RowScope  Manifest +androidx.compose.foundation.layout.RowScope  buttonColors +androidx.compose.foundation.layout.RowScope  launch +androidx.compose.foundation.layout.RowScope  startBackgroundCameraService +androidx.compose.foundation.layout.RowScope  stopBackgroundCameraService +androidx.compose.foundation.layout.RowScope  weight +androidx.compose.foundation.layout.RowScope  BackgroundCameraService androidx.compose.material3  Build androidx.compose.material3  Button androidx.compose.material3  ButtonColors androidx.compose.material3  ButtonDefaults androidx.compose.material3  apply androidx.compose.material3  buttonColors androidx.compose.material3  startBackgroundCameraService androidx.compose.material3  stopBackgroundCameraService androidx.compose.material3  weight androidx.compose.material3  buttonColors )androidx.compose.material3.ButtonDefaults  error &androidx.compose.material3.ColorScheme  primary &androidx.compose.material3.ColorScheme  titleMedium %androidx.compose.material3.Typography  BackgroundCameraService androidx.compose.runtime  Build androidx.compose.runtime  Button androidx.compose.runtime  ButtonDefaults androidx.compose.runtime  apply androidx.compose.runtime  buttonColors androidx.compose.runtime  startBackgroundCameraService androidx.compose.runtime  stopBackgroundCameraService androidx.compose.runtime  weight androidx.compose.runtime  weight androidx.compose.ui.Modifier  weight &androidx.compose.ui.Modifier.Companion  ActivityCompat androidx.core.app  NotificationCompat androidx.core.app  checkSelfPermission  androidx.core.app.ActivityCompat  Builder $androidx.core.app.NotificationCompat  	addAction ,androidx.core.app.NotificationCompat.Builder  build ,androidx.core.app.NotificationCompat.Builder  setContentIntent ,androidx.core.app.NotificationCompat.Builder  setContentText ,androidx.core.app.NotificationCompat.Builder  setContentTitle ,androidx.core.app.NotificationCompat.Builder  
setOngoing ,androidx.core.app.NotificationCompat.Builder  setSmallIcon ,androidx.core.app.NotificationCompat.Builder  checkSelfPermission #androidx.core.content.ContextCompat  ACTION_START_SERVICE com.example.myfirstapp  ACTION_STOP_SERVICE com.example.myfirstapp  ActivityCompat com.example.myfirstapp  BackgroundCameraService com.example.myfirstapp  Boolean com.example.myfirstapp  Build com.example.myfirstapp  Button com.example.myfirstapp  ButtonDefaults com.example.myfirstapp  	ByteArray com.example.myfirstapp  CAPTURE_INTERVAL com.example.myfirstapp  
CHANNEL_ID com.example.myfirstapp  CameraCaptureSession com.example.myfirstapp  CameraCharacteristics com.example.myfirstapp  CameraDevice com.example.myfirstapp  
CameraManager com.example.myfirstapp  CaptureRequest com.example.myfirstapp  CoroutineScope com.example.myfirstapp  Handler com.example.myfirstapp  
HandlerThread com.example.myfirstapp  IBinder com.example.myfirstapp  ImageFormat com.example.myfirstapp  ImageReader com.example.myfirstapp  Int com.example.myfirstapp  Intent com.example.myfirstapp  InterruptedException com.example.myfirstapp  Job com.example.myfirstapp  Log com.example.myfirstapp  NOTIFICATION_ID com.example.myfirstapp  Notification com.example.myfirstapp  NotificationChannel com.example.myfirstapp  NotificationCompat com.example.myfirstapp  NotificationManager com.example.myfirstapp  PackageManager com.example.myfirstapp  
PendingIntent com.example.myfirstapp  RuntimeException com.example.myfirstapp  START_STICKY com.example.myfirstapp  	Semaphore com.example.myfirstapp  Service com.example.myfirstapp  
SupervisorJob com.example.myfirstapp  Surface com.example.myfirstapp  TAG com.example.myfirstapp  TimeUnit com.example.myfirstapp  also com.example.myfirstapp  android com.example.myfirstapp  apply com.example.myfirstapp  buttonColors com.example.myfirstapp  cameraDevice com.example.myfirstapp  cameraOpenCloseLock com.example.myfirstapp  cancel com.example.myfirstapp  captureSession com.example.myfirstapp  captureStillPicture com.example.myfirstapp  createCameraPreviewSession com.example.myfirstapp  delay com.example.myfirstapp  firstOrNull com.example.myfirstapp  isServiceRunning com.example.myfirstapp  startBackgroundCameraService com.example.myfirstapp  stopBackgroundCameraService com.example.myfirstapp  updateNotification com.example.myfirstapp  weight com.example.myfirstapp  ACTION_START_SERVICE .com.example.myfirstapp.BackgroundCameraService  ACTION_STOP_SERVICE .com.example.myfirstapp.BackgroundCameraService  ActivityCompat .com.example.myfirstapp.BackgroundCameraService  BackgroundCameraService .com.example.myfirstapp.BackgroundCameraService  Boolean .com.example.myfirstapp.BackgroundCameraService  Build .com.example.myfirstapp.BackgroundCameraService  	ByteArray .com.example.myfirstapp.BackgroundCameraService  CAPTURE_INTERVAL .com.example.myfirstapp.BackgroundCameraService  
CHANNEL_ID .com.example.myfirstapp.BackgroundCameraService  CameraCaptureSession .com.example.myfirstapp.BackgroundCameraService  CameraCharacteristics .com.example.myfirstapp.BackgroundCameraService  CameraDevice .com.example.myfirstapp.BackgroundCameraService  
CameraManager .com.example.myfirstapp.BackgroundCameraService  CaptureRequest .com.example.myfirstapp.BackgroundCameraService  	Companion .com.example.myfirstapp.BackgroundCameraService  Context .com.example.myfirstapp.BackgroundCameraService  CoroutineScope .com.example.myfirstapp.BackgroundCameraService  Dispatchers .com.example.myfirstapp.BackgroundCameraService  	Exception .com.example.myfirstapp.BackgroundCameraService  File .com.example.myfirstapp.BackgroundCameraService  FileOutputStream .com.example.myfirstapp.BackgroundCameraService  Handler .com.example.myfirstapp.BackgroundCameraService  
HandlerThread .com.example.myfirstapp.BackgroundCameraService  IBinder .com.example.myfirstapp.BackgroundCameraService  ImageFormat .com.example.myfirstapp.BackgroundCameraService  ImageReader .com.example.myfirstapp.BackgroundCameraService  Int .com.example.myfirstapp.BackgroundCameraService  Intent .com.example.myfirstapp.BackgroundCameraService  InterruptedException .com.example.myfirstapp.BackgroundCameraService  Job .com.example.myfirstapp.BackgroundCameraService  Log .com.example.myfirstapp.BackgroundCameraService  MainActivity .com.example.myfirstapp.BackgroundCameraService  Manifest .com.example.myfirstapp.BackgroundCameraService  NOTIFICATION_ID .com.example.myfirstapp.BackgroundCameraService  Notification .com.example.myfirstapp.BackgroundCameraService  NotificationChannel .com.example.myfirstapp.BackgroundCameraService  NotificationCompat .com.example.myfirstapp.BackgroundCameraService  NotificationManager .com.example.myfirstapp.BackgroundCameraService  PackageManager .com.example.myfirstapp.BackgroundCameraService  
PendingIntent .com.example.myfirstapp.BackgroundCameraService  RuntimeException .com.example.myfirstapp.BackgroundCameraService  START_STICKY .com.example.myfirstapp.BackgroundCameraService  	Semaphore .com.example.myfirstapp.BackgroundCameraService  String .com.example.myfirstapp.BackgroundCameraService  
SupervisorJob .com.example.myfirstapp.BackgroundCameraService  Surface .com.example.myfirstapp.BackgroundCameraService  System .com.example.myfirstapp.BackgroundCameraService  TAG .com.example.myfirstapp.BackgroundCameraService  TimeUnit .com.example.myfirstapp.BackgroundCameraService  also .com.example.myfirstapp.BackgroundCameraService  android .com.example.myfirstapp.BackgroundCameraService  apply .com.example.myfirstapp.BackgroundCameraService  backgroundHandler .com.example.myfirstapp.BackgroundCameraService  backgroundThread .com.example.myfirstapp.BackgroundCameraService  cameraDevice .com.example.myfirstapp.BackgroundCameraService  
cameraManager .com.example.myfirstapp.BackgroundCameraService  cameraOpenCloseLock .com.example.myfirstapp.BackgroundCameraService  cancel .com.example.myfirstapp.BackgroundCameraService  
captureJob .com.example.myfirstapp.BackgroundCameraService  captureSession .com.example.myfirstapp.BackgroundCameraService  captureStillPicture .com.example.myfirstapp.BackgroundCameraService  closeCamera .com.example.myfirstapp.BackgroundCameraService  createCameraPreviewSession .com.example.myfirstapp.BackgroundCameraService  createNotification .com.example.myfirstapp.BackgroundCameraService  createNotificationChannel .com.example.myfirstapp.BackgroundCameraService  delay .com.example.myfirstapp.BackgroundCameraService  firstOrNull .com.example.myfirstapp.BackgroundCameraService  getCameraId .com.example.myfirstapp.BackgroundCameraService  getExternalFilesDir .com.example.myfirstapp.BackgroundCameraService  getSystemService .com.example.myfirstapp.BackgroundCameraService  hasRequiredPermissions .com.example.myfirstapp.BackgroundCameraService  imageAvailableListener .com.example.myfirstapp.BackgroundCameraService  imageReader .com.example.myfirstapp.BackgroundCameraService  initializeCamera .com.example.myfirstapp.BackgroundCameraService  isServiceRunning .com.example.myfirstapp.BackgroundCameraService  java .com.example.myfirstapp.BackgroundCameraService  launch .com.example.myfirstapp.BackgroundCameraService  listOf .com.example.myfirstapp.BackgroundCameraService  
openCamera .com.example.myfirstapp.BackgroundCameraService  saveImageAndUpload .com.example.myfirstapp.BackgroundCameraService  serviceScope .com.example.myfirstapp.BackgroundCameraService  startBackgroundThread .com.example.myfirstapp.BackgroundCameraService  startCameraService .com.example.myfirstapp.BackgroundCameraService  startForeground .com.example.myfirstapp.BackgroundCameraService  startPeriodicCapture .com.example.myfirstapp.BackgroundCameraService  
stateCallback .com.example.myfirstapp.BackgroundCameraService  stopBackgroundThread .com.example.myfirstapp.BackgroundCameraService  stopCameraService .com.example.myfirstapp.BackgroundCameraService  stopForeground .com.example.myfirstapp.BackgroundCameraService  stopSelf .com.example.myfirstapp.BackgroundCameraService  updateNotification .com.example.myfirstapp.BackgroundCameraService  uploadImageToCloudinary .com.example.myfirstapp.BackgroundCameraService  use .com.example.myfirstapp.BackgroundCameraService  
StateCallback Ccom.example.myfirstapp.BackgroundCameraService.CameraCaptureSession  
StateCallback ;com.example.myfirstapp.BackgroundCameraService.CameraDevice  ACTION_START_SERVICE 8com.example.myfirstapp.BackgroundCameraService.Companion  ACTION_STOP_SERVICE 8com.example.myfirstapp.BackgroundCameraService.Companion  ActivityCompat 8com.example.myfirstapp.BackgroundCameraService.Companion  BackgroundCameraService 8com.example.myfirstapp.BackgroundCameraService.Companion  Build 8com.example.myfirstapp.BackgroundCameraService.Companion  	ByteArray 8com.example.myfirstapp.BackgroundCameraService.Companion  CAPTURE_INTERVAL 8com.example.myfirstapp.BackgroundCameraService.Companion  
CHANNEL_ID 8com.example.myfirstapp.BackgroundCameraService.Companion  CameraCharacteristics 8com.example.myfirstapp.BackgroundCameraService.Companion  CameraDevice 8com.example.myfirstapp.BackgroundCameraService.Companion  CaptureRequest 8com.example.myfirstapp.BackgroundCameraService.Companion  Context 8com.example.myfirstapp.BackgroundCameraService.Companion  CoroutineScope 8com.example.myfirstapp.BackgroundCameraService.Companion  Dispatchers 8com.example.myfirstapp.BackgroundCameraService.Companion  File 8com.example.myfirstapp.BackgroundCameraService.Companion  FileOutputStream 8com.example.myfirstapp.BackgroundCameraService.Companion  Handler 8com.example.myfirstapp.BackgroundCameraService.Companion  
HandlerThread 8com.example.myfirstapp.BackgroundCameraService.Companion  ImageFormat 8com.example.myfirstapp.BackgroundCameraService.Companion  ImageReader 8com.example.myfirstapp.BackgroundCameraService.Companion  Intent 8com.example.myfirstapp.BackgroundCameraService.Companion  Log 8com.example.myfirstapp.BackgroundCameraService.Companion  MainActivity 8com.example.myfirstapp.BackgroundCameraService.Companion  Manifest 8com.example.myfirstapp.BackgroundCameraService.Companion  NOTIFICATION_ID 8com.example.myfirstapp.BackgroundCameraService.Companion  NotificationChannel 8com.example.myfirstapp.BackgroundCameraService.Companion  NotificationCompat 8com.example.myfirstapp.BackgroundCameraService.Companion  NotificationManager 8com.example.myfirstapp.BackgroundCameraService.Companion  PackageManager 8com.example.myfirstapp.BackgroundCameraService.Companion  
PendingIntent 8com.example.myfirstapp.BackgroundCameraService.Companion  RuntimeException 8com.example.myfirstapp.BackgroundCameraService.Companion  START_STICKY 8com.example.myfirstapp.BackgroundCameraService.Companion  	Semaphore 8com.example.myfirstapp.BackgroundCameraService.Companion  
SupervisorJob 8com.example.myfirstapp.BackgroundCameraService.Companion  System 8com.example.myfirstapp.BackgroundCameraService.Companion  TAG 8com.example.myfirstapp.BackgroundCameraService.Companion  TimeUnit 8com.example.myfirstapp.BackgroundCameraService.Companion  also 8com.example.myfirstapp.BackgroundCameraService.Companion  android 8com.example.myfirstapp.BackgroundCameraService.Companion  apply 8com.example.myfirstapp.BackgroundCameraService.Companion  cameraDevice 8com.example.myfirstapp.BackgroundCameraService.Companion  cameraOpenCloseLock 8com.example.myfirstapp.BackgroundCameraService.Companion  cancel 8com.example.myfirstapp.BackgroundCameraService.Companion  captureSession 8com.example.myfirstapp.BackgroundCameraService.Companion  captureStillPicture 8com.example.myfirstapp.BackgroundCameraService.Companion  createCameraPreviewSession 8com.example.myfirstapp.BackgroundCameraService.Companion  delay 8com.example.myfirstapp.BackgroundCameraService.Companion  firstOrNull 8com.example.myfirstapp.BackgroundCameraService.Companion  isServiceRunning 8com.example.myfirstapp.BackgroundCameraService.Companion  java 8com.example.myfirstapp.BackgroundCameraService.Companion  launch 8com.example.myfirstapp.BackgroundCameraService.Companion  listOf 8com.example.myfirstapp.BackgroundCameraService.Companion  updateNotification 8com.example.myfirstapp.BackgroundCameraService.Companion  uploadImageToCloudinary 8com.example.myfirstapp.BackgroundCameraService.Companion  use 8com.example.myfirstapp.BackgroundCameraService.Companion  media 6com.example.myfirstapp.BackgroundCameraService.android  Image <com.example.myfirstapp.BackgroundCameraService.android.media  
StateCallback +com.example.myfirstapp.CameraCaptureSession  
StateCallback #com.example.myfirstapp.CameraDevice  media com.example.myfirstapp.android  Image $com.example.myfirstapp.android.media  absolutePath java.io.File  delete java.io.File  write java.io.FileOutputStream  InterruptedException 	java.lang  Runnable 	java.lang  RuntimeException 	java.lang  <SAM-CONSTRUCTOR> java.lang.Runnable  join java.lang.Thread  start java.lang.Thread  	remaining java.nio.Buffer  get java.nio.ByteBuffer  ACTION_START_SERVICE 	java.util  ACTION_STOP_SERVICE 	java.util  ActivityCompat 	java.util  BackgroundCameraService 	java.util  Boolean 	java.util  Build 	java.util  	ByteArray 	java.util  CAPTURE_INTERVAL 	java.util  
CHANNEL_ID 	java.util  CameraCaptureSession 	java.util  CameraCharacteristics 	java.util  CameraDevice 	java.util  
CameraManager 	java.util  CaptureRequest 	java.util  Context 	java.util  CoroutineScope 	java.util  Dispatchers 	java.util  	Exception 	java.util  File 	java.util  FileOutputStream 	java.util  Handler 	java.util  
HandlerThread 	java.util  IBinder 	java.util  ImageFormat 	java.util  ImageReader 	java.util  Int 	java.util  Intent 	java.util  InterruptedException 	java.util  Job 	java.util  Log 	java.util  MainActivity 	java.util  Manifest 	java.util  NOTIFICATION_ID 	java.util  Notification 	java.util  NotificationChannel 	java.util  NotificationCompat 	java.util  NotificationManager 	java.util  PackageManager 	java.util  
PendingIntent 	java.util  RuntimeException 	java.util  START_STICKY 	java.util  	Semaphore 	java.util  Service 	java.util  String 	java.util  
SupervisorJob 	java.util  Surface 	java.util  System 	java.util  TAG 	java.util  TimeUnit 	java.util  also 	java.util  android 	java.util  apply 	java.util  cameraDevice 	java.util  cameraOpenCloseLock 	java.util  cancel 	java.util  captureSession 	java.util  captureStillPicture 	java.util  createCameraPreviewSession 	java.util  delay 	java.util  firstOrNull 	java.util  isServiceRunning 	java.util  java 	java.util  launch 	java.util  listOf 	java.util  updateNotification 	java.util  uploadImageToCloudinary 	java.util  use 	java.util  
StateCallback java.util.CameraCaptureSession  
StateCallback java.util.CameraDevice  media java.util.android  Image java.util.android.media  	Semaphore java.util.concurrent  acquire java.util.concurrent.Semaphore  release java.util.concurrent.Semaphore  
tryAcquire java.util.concurrent.Semaphore  MILLISECONDS java.util.concurrent.TimeUnit  also kotlin  apply kotlin  get kotlin.Array  or 
kotlin.Int  firstOrNull kotlin.collections  CoroutineContext kotlin.coroutines  plus "kotlin.coroutines.CoroutineContext  firstOrNull 
kotlin.ranges  java kotlin.reflect.KClass  firstOrNull kotlin.sequences  firstOrNull kotlin.text  ACTION_START_SERVICE kotlinx.coroutines  ACTION_STOP_SERVICE kotlinx.coroutines  ActivityCompat kotlinx.coroutines  BackgroundCameraService kotlinx.coroutines  Boolean kotlinx.coroutines  Build kotlinx.coroutines  	ByteArray kotlinx.coroutines  CAPTURE_INTERVAL kotlinx.coroutines  
CHANNEL_ID kotlinx.coroutines  CameraCaptureSession kotlinx.coroutines  CameraCharacteristics kotlinx.coroutines  CameraDevice kotlinx.coroutines  
CameraManager kotlinx.coroutines  CaptureRequest kotlinx.coroutines  CompletableJob kotlinx.coroutines  Context kotlinx.coroutines  Delay kotlinx.coroutines  	Exception kotlinx.coroutines  File kotlinx.coroutines  FileOutputStream kotlinx.coroutines  Handler kotlinx.coroutines  
HandlerThread kotlinx.coroutines  IBinder kotlinx.coroutines  ImageFormat kotlinx.coroutines  ImageReader kotlinx.coroutines  Int kotlinx.coroutines  Intent kotlinx.coroutines  InterruptedException kotlinx.coroutines  Log kotlinx.coroutines  MainActivity kotlinx.coroutines  Manifest kotlinx.coroutines  NOTIFICATION_ID kotlinx.coroutines  Notification kotlinx.coroutines  NotificationChannel kotlinx.coroutines  NotificationCompat kotlinx.coroutines  NotificationManager kotlinx.coroutines  PackageManager kotlinx.coroutines  
PendingIntent kotlinx.coroutines  RuntimeException kotlinx.coroutines  START_STICKY kotlinx.coroutines  	Semaphore kotlinx.coroutines  Service kotlinx.coroutines  String kotlinx.coroutines  
SupervisorJob kotlinx.coroutines  Surface kotlinx.coroutines  System kotlinx.coroutines  TAG kotlinx.coroutines  TimeUnit kotlinx.coroutines  also kotlinx.coroutines  android kotlinx.coroutines  apply kotlinx.coroutines  cameraDevice kotlinx.coroutines  cameraOpenCloseLock kotlinx.coroutines  cancel kotlinx.coroutines  captureSession kotlinx.coroutines  captureStillPicture kotlinx.coroutines  createCameraPreviewSession kotlinx.coroutines  delay kotlinx.coroutines  firstOrNull kotlinx.coroutines  isServiceRunning kotlinx.coroutines  java kotlinx.coroutines  listOf kotlinx.coroutines  updateNotification kotlinx.coroutines  uploadImageToCloudinary kotlinx.coroutines  use kotlinx.coroutines  
StateCallback 'kotlinx.coroutines.CameraCaptureSession  
StateCallback kotlinx.coroutines.CameraDevice  plus &kotlinx.coroutines.CoroutineDispatcher  CAPTURE_INTERVAL !kotlinx.coroutines.CoroutineScope  Log !kotlinx.coroutines.CoroutineScope  TAG !kotlinx.coroutines.CoroutineScope  cancel !kotlinx.coroutines.CoroutineScope  captureStillPicture !kotlinx.coroutines.CoroutineScope  delay !kotlinx.coroutines.CoroutineScope  isServiceRunning !kotlinx.coroutines.CoroutineScope  updateNotification !kotlinx.coroutines.CoroutineScope  cancel kotlinx.coroutines.Job  plus *kotlinx.coroutines.MainCoroutineDispatcher  media kotlinx.coroutines.android  Image  kotlinx.coroutines.android.media  BackgroundCameraService okhttp3  Build okhttp3  Button okhttp3  ButtonDefaults okhttp3  apply okhttp3  buttonColors okhttp3  startBackgroundCameraService okhttp3  stopBackgroundCameraService okhttp3  weight okhttp3  ACTION_CAPTURE_NOW android.app.Service  BroadcastReceiver android.content  BackgroundCameraService !android.content.BroadcastReceiver  Build !android.content.BroadcastReceiver  Intent !android.content.BroadcastReceiver  Log !android.content.BroadcastReceiver  TAG !android.content.BroadcastReceiver  apply !android.content.BroadcastReceiver  java !android.content.BroadcastReceiver  ACTION_CAPTURE_NOW android.content.Context  ACTION_CAPTURE_NOW android.content.ContextWrapper  ACTION_BOOT_COMPLETED android.content.Intent  ACTION_SCREEN_ON android.content.Intent  ACTION_USER_PRESENT android.content.Intent  ACTION_CAPTURE_NOW android.hardware.camera2  LENS_FACING_FRONT .android.hardware.camera2.CameraCharacteristics  LENS_FACING_FRONT 'android.hardware.camera2.CameraMetadata  ACTION_CAPTURE_NOW com.example.myfirstapp  BroadcastReceiver com.example.myfirstapp  ScreenUnlockReceiver com.example.myfirstapp  ACTION_CAPTURE_NOW .com.example.myfirstapp.BackgroundCameraService  ACTION_CAPTURE_NOW 8com.example.myfirstapp.BackgroundCameraService.Companion  BackgroundCameraService +com.example.myfirstapp.ScreenUnlockReceiver  Build +com.example.myfirstapp.ScreenUnlockReceiver  Context +com.example.myfirstapp.ScreenUnlockReceiver  Intent +com.example.myfirstapp.ScreenUnlockReceiver  Log +com.example.myfirstapp.ScreenUnlockReceiver  TAG +com.example.myfirstapp.ScreenUnlockReceiver  apply +com.example.myfirstapp.ScreenUnlockReceiver  java +com.example.myfirstapp.ScreenUnlockReceiver  startCameraService +com.example.myfirstapp.ScreenUnlockReceiver  triggerPhotoCapture +com.example.myfirstapp.ScreenUnlockReceiver  BackgroundCameraService 5com.example.myfirstapp.ScreenUnlockReceiver.Companion  Build 5com.example.myfirstapp.ScreenUnlockReceiver.Companion  Intent 5com.example.myfirstapp.ScreenUnlockReceiver.Companion  Log 5com.example.myfirstapp.ScreenUnlockReceiver.Companion  TAG 5com.example.myfirstapp.ScreenUnlockReceiver.Companion  apply 5com.example.myfirstapp.ScreenUnlockReceiver.Companion  java 5com.example.myfirstapp.ScreenUnlockReceiver.Companion  ACTION_CAPTURE_NOW 	java.util  ACTION_CAPTURE_NOW kotlinx.coroutines                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       