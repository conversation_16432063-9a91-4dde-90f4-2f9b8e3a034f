  Manifest android  CAMERA android.Manifest.permission  Activity android.app  ImageUploadApp android.app.Activity  MyFirstAppTheme android.app.Activity  onCreate android.app.Activity  
setContent android.app.Activity  ContentResolver android.content  Context android.content  openInputStream android.content.ContentResolver  ImageUploadApp android.content.Context  MyFirstAppTheme android.content.Context  contentResolver android.content.Context  getExternalFilesDir android.content.Context  packageName android.content.Context  
setContent android.content.Context  ImageUploadApp android.content.ContextWrapper  MyFirstAppTheme android.content.ContextWrapper  
setContent android.content.ContextWrapper  Uri android.net  let android.net.Uri  Build 
android.os  Bundle 
android.os  SDK_INT android.os.Build.VERSION  S android.os.Build.VERSION_CODES  ImageUploadApp  android.view.ContextThemeWrapper  MyFirstAppTheme  android.view.ContextThemeWrapper  
setContent  android.view.ContextThemeWrapper  ComponentActivity androidx.activity  Bundle #androidx.activity.ComponentActivity  	Companion #androidx.activity.ComponentActivity  ImageUploadApp #androidx.activity.ComponentActivity  MyFirstAppTheme #androidx.activity.ComponentActivity  onCreate #androidx.activity.ComponentActivity  
setContent #androidx.activity.ComponentActivity  ImageUploadApp -androidx.activity.ComponentActivity.Companion  MyFirstAppTheme -androidx.activity.ComponentActivity.Companion  
setContent -androidx.activity.ComponentActivity.Companion  ManagedActivityResultLauncher androidx.activity.compose  !rememberLauncherForActivityResult androidx.activity.compose  
setContent androidx.activity.compose  launch 7androidx.activity.compose.ManagedActivityResultLauncher  ActivityResultContracts !androidx.activity.result.contract  
GetContent 9androidx.activity.result.contract.ActivityResultContracts  RequestPermission 9androidx.activity.result.contract.ActivityResultContracts  TakePicture 9androidx.activity.result.contract.ActivityResultContracts  Image androidx.compose.foundation  	clickable androidx.compose.foundation  isSystemInDarkTheme androidx.compose.foundation  ActivityResultContracts "androidx.compose.foundation.layout  	Alignment "androidx.compose.foundation.layout  Arrangement "androidx.compose.foundation.layout  Box "androidx.compose.foundation.layout  BoxScope "androidx.compose.foundation.layout  Bundle "androidx.compose.foundation.layout  Card "androidx.compose.foundation.layout  CardDefaults "androidx.compose.foundation.layout  CircularProgressIndicator "androidx.compose.foundation.layout  Column "androidx.compose.foundation.layout  ColumnScope "androidx.compose.foundation.layout  ComponentActivity "androidx.compose.foundation.layout  
Composable "androidx.compose.foundation.layout  ContentScale "androidx.compose.foundation.layout  Context "androidx.compose.foundation.layout  Dispatchers "androidx.compose.foundation.layout  	Exception "androidx.compose.foundation.layout  ExperimentalMaterial3Api "androidx.compose.foundation.layout  File "androidx.compose.foundation.layout  FileOutputStream "androidx.compose.foundation.layout  FileProvider "androidx.compose.foundation.layout  FloatingActionButton "androidx.compose.foundation.layout  
FontWeight "androidx.compose.foundation.layout  Icon "androidx.compose.foundation.layout  Icons "androidx.compose.foundation.layout  Image "androidx.compose.foundation.layout  ImageUploadApp "androidx.compose.foundation.layout  InputStream "androidx.compose.foundation.layout  
JSONObject "androidx.compose.foundation.layout  
LazyColumn "androidx.compose.foundation.layout  Manifest "androidx.compose.foundation.layout  
MaterialTheme "androidx.compose.foundation.layout  ModalBottomSheet "androidx.compose.foundation.layout  Modifier "androidx.compose.foundation.layout  
MultipartBody "androidx.compose.foundation.layout  MyFirstAppTheme "androidx.compose.foundation.layout  OkHttpClient "androidx.compose.foundation.layout  OptIn "androidx.compose.foundation.layout  
PaddingValues "androidx.compose.foundation.layout  Request "androidx.compose.foundation.layout  RoundedCornerShape "androidx.compose.foundation.layout  Row "androidx.compose.foundation.layout  RowScope "androidx.compose.foundation.layout  Scaffold "androidx.compose.foundation.layout  SnackbarHost "androidx.compose.foundation.layout  SnackbarHostState "androidx.compose.foundation.layout  Spacer "androidx.compose.foundation.layout  String "androidx.compose.foundation.layout  System "androidx.compose.foundation.layout  Text "androidx.compose.foundation.layout  Unit "androidx.compose.foundation.layout  Uri "androidx.compose.foundation.layout  
asRequestBody "androidx.compose.foundation.layout  
cardElevation "androidx.compose.foundation.layout  	clickable "androidx.compose.foundation.layout  clip "androidx.compose.foundation.layout  copyTo "androidx.compose.foundation.layout  
copyUriToFile "androidx.compose.foundation.layout  fillMaxSize "androidx.compose.foundation.layout  fillMaxWidth "androidx.compose.foundation.layout  getValue "androidx.compose.foundation.layout  height "androidx.compose.foundation.layout  java "androidx.compose.foundation.layout  launch "androidx.compose.foundation.layout  let "androidx.compose.foundation.layout  listOf "androidx.compose.foundation.layout  mutableStateOf "androidx.compose.foundation.layout  padding "androidx.compose.foundation.layout  plus "androidx.compose.foundation.layout  println "androidx.compose.foundation.layout  provideDelegate "androidx.compose.foundation.layout  remember "androidx.compose.foundation.layout  rememberAsyncImagePainter "androidx.compose.foundation.layout  rememberCoroutineScope "androidx.compose.foundation.layout  setValue "androidx.compose.foundation.layout  size "androidx.compose.foundation.layout  spacedBy "androidx.compose.foundation.layout  toMediaTypeOrNull "androidx.compose.foundation.layout  uploadImageToCloudinary "androidx.compose.foundation.layout  use "androidx.compose.foundation.layout  withContext "androidx.compose.foundation.layout  
Horizontal .androidx.compose.foundation.layout.Arrangement  HorizontalOrVertical .androidx.compose.foundation.layout.Arrangement  Vertical .androidx.compose.foundation.layout.Arrangement  spacedBy .androidx.compose.foundation.layout.Arrangement  	Alignment +androidx.compose.foundation.layout.BoxScope  Column +androidx.compose.foundation.layout.BoxScope  Icon +androidx.compose.foundation.layout.BoxScope  Icons +androidx.compose.foundation.layout.BoxScope  
MaterialTheme +androidx.compose.foundation.layout.BoxScope  Modifier +androidx.compose.foundation.layout.BoxScope  Photo +androidx.compose.foundation.layout.BoxScope  Spacer +androidx.compose.foundation.layout.BoxScope  Text +androidx.compose.foundation.layout.BoxScope  dp +androidx.compose.foundation.layout.BoxScope  height +androidx.compose.foundation.layout.BoxScope  size +androidx.compose.foundation.layout.BoxScope  	Alignment .androidx.compose.foundation.layout.ColumnScope  Arrangement .androidx.compose.foundation.layout.ColumnScope  Box .androidx.compose.foundation.layout.ColumnScope  Camera .androidx.compose.foundation.layout.ColumnScope  Card .androidx.compose.foundation.layout.ColumnScope  CardDefaults .androidx.compose.foundation.layout.ColumnScope  Column .androidx.compose.foundation.layout.ColumnScope  ContentScale .androidx.compose.foundation.layout.ColumnScope  
FontWeight .androidx.compose.foundation.layout.ColumnScope  Icon .androidx.compose.foundation.layout.ColumnScope  Icons .androidx.compose.foundation.layout.ColumnScope  Image .androidx.compose.foundation.layout.ColumnScope  
LazyColumn .androidx.compose.foundation.layout.ColumnScope  Manifest .androidx.compose.foundation.layout.ColumnScope  
MaterialTheme .androidx.compose.foundation.layout.ColumnScope  Modifier .androidx.compose.foundation.layout.ColumnScope  Photo .androidx.compose.foundation.layout.ColumnScope  RoundedCornerShape .androidx.compose.foundation.layout.ColumnScope  Row .androidx.compose.foundation.layout.ColumnScope  Spacer .androidx.compose.foundation.layout.ColumnScope  Text .androidx.compose.foundation.layout.ColumnScope  
cardElevation .androidx.compose.foundation.layout.ColumnScope  	clickable .androidx.compose.foundation.layout.ColumnScope  clip .androidx.compose.foundation.layout.ColumnScope  dp .androidx.compose.foundation.layout.ColumnScope  fillMaxSize .androidx.compose.foundation.layout.ColumnScope  fillMaxWidth .androidx.compose.foundation.layout.ColumnScope  height .androidx.compose.foundation.layout.ColumnScope  items .androidx.compose.foundation.layout.ColumnScope  padding .androidx.compose.foundation.layout.ColumnScope  rememberAsyncImagePainter .androidx.compose.foundation.layout.ColumnScope  size .androidx.compose.foundation.layout.ColumnScope  spacedBy .androidx.compose.foundation.layout.ColumnScope  Camera +androidx.compose.foundation.layout.RowScope  Icon +androidx.compose.foundation.layout.RowScope  Icons +androidx.compose.foundation.layout.RowScope  
MaterialTheme +androidx.compose.foundation.layout.RowScope  Modifier +androidx.compose.foundation.layout.RowScope  Photo +androidx.compose.foundation.layout.RowScope  Text +androidx.compose.foundation.layout.RowScope  dp +androidx.compose.foundation.layout.RowScope  padding +androidx.compose.foundation.layout.RowScope  
LazyColumn  androidx.compose.foundation.lazy  
LazyItemScope  androidx.compose.foundation.lazy  
LazyListScope  androidx.compose.foundation.lazy  items  androidx.compose.foundation.lazy  Card .androidx.compose.foundation.lazy.LazyItemScope  CardDefaults .androidx.compose.foundation.lazy.LazyItemScope  ContentScale .androidx.compose.foundation.lazy.LazyItemScope  Image .androidx.compose.foundation.lazy.LazyItemScope  Modifier .androidx.compose.foundation.lazy.LazyItemScope  RoundedCornerShape .androidx.compose.foundation.lazy.LazyItemScope  
cardElevation .androidx.compose.foundation.lazy.LazyItemScope  	clickable .androidx.compose.foundation.lazy.LazyItemScope  clip .androidx.compose.foundation.lazy.LazyItemScope  dp .androidx.compose.foundation.lazy.LazyItemScope  fillMaxWidth .androidx.compose.foundation.lazy.LazyItemScope  height .androidx.compose.foundation.lazy.LazyItemScope  rememberAsyncImagePainter .androidx.compose.foundation.lazy.LazyItemScope  Card .androidx.compose.foundation.lazy.LazyListScope  CardDefaults .androidx.compose.foundation.lazy.LazyListScope  ContentScale .androidx.compose.foundation.lazy.LazyListScope  Image .androidx.compose.foundation.lazy.LazyListScope  Modifier .androidx.compose.foundation.lazy.LazyListScope  RoundedCornerShape .androidx.compose.foundation.lazy.LazyListScope  
cardElevation .androidx.compose.foundation.lazy.LazyListScope  	clickable .androidx.compose.foundation.lazy.LazyListScope  clip .androidx.compose.foundation.lazy.LazyListScope  dp .androidx.compose.foundation.lazy.LazyListScope  fillMaxWidth .androidx.compose.foundation.lazy.LazyListScope  height .androidx.compose.foundation.lazy.LazyListScope  items .androidx.compose.foundation.lazy.LazyListScope  rememberAsyncImagePainter .androidx.compose.foundation.lazy.LazyListScope  RoundedCornerShape !androidx.compose.foundation.shape  Icons androidx.compose.material.icons  Default %androidx.compose.material.icons.Icons  Filled %androidx.compose.material.icons.Icons  Add ,androidx.compose.material.icons.Icons.Filled  Camera ,androidx.compose.material.icons.Icons.Filled  Photo ,androidx.compose.material.icons.Icons.Filled  Add &androidx.compose.material.icons.filled  Camera &androidx.compose.material.icons.filled  Photo &androidx.compose.material.icons.filled  ActivityResultContracts androidx.compose.material3  	Alignment androidx.compose.material3  Arrangement androidx.compose.material3  Box androidx.compose.material3  Bundle androidx.compose.material3  Card androidx.compose.material3  CardDefaults androidx.compose.material3  
CardElevation androidx.compose.material3  CircularProgressIndicator androidx.compose.material3  ColorScheme androidx.compose.material3  Column androidx.compose.material3  ComponentActivity androidx.compose.material3  
Composable androidx.compose.material3  ContentScale androidx.compose.material3  Context androidx.compose.material3  Dispatchers androidx.compose.material3  	Exception androidx.compose.material3  ExperimentalMaterial3Api androidx.compose.material3  File androidx.compose.material3  FileOutputStream androidx.compose.material3  FileProvider androidx.compose.material3  FloatingActionButton androidx.compose.material3  
FontWeight androidx.compose.material3  Icon androidx.compose.material3  Icons androidx.compose.material3  Image androidx.compose.material3  ImageUploadApp androidx.compose.material3  InputStream androidx.compose.material3  
JSONObject androidx.compose.material3  
LazyColumn androidx.compose.material3  Manifest androidx.compose.material3  
MaterialTheme androidx.compose.material3  ModalBottomSheet androidx.compose.material3  Modifier androidx.compose.material3  
MultipartBody androidx.compose.material3  MyFirstAppTheme androidx.compose.material3  OkHttpClient androidx.compose.material3  OptIn androidx.compose.material3  Request androidx.compose.material3  RoundedCornerShape androidx.compose.material3  Row androidx.compose.material3  Scaffold androidx.compose.material3  SnackbarHost androidx.compose.material3  SnackbarHostState androidx.compose.material3  SnackbarResult androidx.compose.material3  Spacer androidx.compose.material3  String androidx.compose.material3  System androidx.compose.material3  Text androidx.compose.material3  
Typography androidx.compose.material3  Unit androidx.compose.material3  Uri androidx.compose.material3  
asRequestBody androidx.compose.material3  
cardElevation androidx.compose.material3  	clickable androidx.compose.material3  clip androidx.compose.material3  copyTo androidx.compose.material3  
copyUriToFile androidx.compose.material3  darkColorScheme androidx.compose.material3  dynamicDarkColorScheme androidx.compose.material3  dynamicLightColorScheme androidx.compose.material3  fillMaxSize androidx.compose.material3  fillMaxWidth androidx.compose.material3  getValue androidx.compose.material3  height androidx.compose.material3  java androidx.compose.material3  launch androidx.compose.material3  let androidx.compose.material3  lightColorScheme androidx.compose.material3  listOf androidx.compose.material3  mutableStateOf androidx.compose.material3  padding androidx.compose.material3  plus androidx.compose.material3  println androidx.compose.material3  provideDelegate androidx.compose.material3  remember androidx.compose.material3  rememberAsyncImagePainter androidx.compose.material3  rememberCoroutineScope androidx.compose.material3  setValue androidx.compose.material3  size androidx.compose.material3  spacedBy androidx.compose.material3  toMediaTypeOrNull androidx.compose.material3  uploadImageToCloudinary androidx.compose.material3  use androidx.compose.material3  withContext androidx.compose.material3  
cardElevation 'androidx.compose.material3.CardDefaults  	onPrimary &androidx.compose.material3.ColorScheme  onSurfaceVariant &androidx.compose.material3.ColorScheme  colorScheme (androidx.compose.material3.MaterialTheme  
typography (androidx.compose.material3.MaterialTheme  showSnackbar ,androidx.compose.material3.SnackbarHostState  	bodyLarge %androidx.compose.material3.Typography  
bodyMedium %androidx.compose.material3.Typography  headlineMedium %androidx.compose.material3.Typography  
headlineSmall %androidx.compose.material3.Typography  ActivityResultContracts androidx.compose.runtime  	Alignment androidx.compose.runtime  Arrangement androidx.compose.runtime  Box androidx.compose.runtime  Bundle androidx.compose.runtime  Card androidx.compose.runtime  CardDefaults androidx.compose.runtime  CircularProgressIndicator androidx.compose.runtime  Column androidx.compose.runtime  ComponentActivity androidx.compose.runtime  
Composable androidx.compose.runtime  ContentScale androidx.compose.runtime  Context androidx.compose.runtime  Dispatchers androidx.compose.runtime  	Exception androidx.compose.runtime  ExperimentalMaterial3Api androidx.compose.runtime  File androidx.compose.runtime  FileOutputStream androidx.compose.runtime  FileProvider androidx.compose.runtime  FloatingActionButton androidx.compose.runtime  
FontWeight androidx.compose.runtime  Icon androidx.compose.runtime  Icons androidx.compose.runtime  Image androidx.compose.runtime  ImageUploadApp androidx.compose.runtime  InputStream androidx.compose.runtime  
JSONObject androidx.compose.runtime  
LazyColumn androidx.compose.runtime  Manifest androidx.compose.runtime  
MaterialTheme androidx.compose.runtime  ModalBottomSheet androidx.compose.runtime  Modifier androidx.compose.runtime  
MultipartBody androidx.compose.runtime  MutableState androidx.compose.runtime  MyFirstAppTheme androidx.compose.runtime  OkHttpClient androidx.compose.runtime  OptIn androidx.compose.runtime  ProvidableCompositionLocal androidx.compose.runtime  Request androidx.compose.runtime  RoundedCornerShape androidx.compose.runtime  Row androidx.compose.runtime  Scaffold androidx.compose.runtime  SnackbarHost androidx.compose.runtime  SnackbarHostState androidx.compose.runtime  Spacer androidx.compose.runtime  String androidx.compose.runtime  System androidx.compose.runtime  Text androidx.compose.runtime  Unit androidx.compose.runtime  Uri androidx.compose.runtime  
asRequestBody androidx.compose.runtime  
cardElevation androidx.compose.runtime  	clickable androidx.compose.runtime  clip androidx.compose.runtime  copyTo androidx.compose.runtime  
copyUriToFile androidx.compose.runtime  fillMaxSize androidx.compose.runtime  fillMaxWidth androidx.compose.runtime  getValue androidx.compose.runtime  height androidx.compose.runtime  java androidx.compose.runtime  launch androidx.compose.runtime  let androidx.compose.runtime  listOf androidx.compose.runtime  mutableStateOf androidx.compose.runtime  padding androidx.compose.runtime  plus androidx.compose.runtime  println androidx.compose.runtime  provideDelegate androidx.compose.runtime  remember androidx.compose.runtime  rememberAsyncImagePainter androidx.compose.runtime  rememberCoroutineScope androidx.compose.runtime  setValue androidx.compose.runtime  size androidx.compose.runtime  spacedBy androidx.compose.runtime  toMediaTypeOrNull androidx.compose.runtime  uploadImageToCloudinary androidx.compose.runtime  use androidx.compose.runtime  withContext androidx.compose.runtime  
getCurrent )androidx.compose.runtime.CompositionLocal  setValue %androidx.compose.runtime.MutableState  current 3androidx.compose.runtime.ProvidableCompositionLocal  ComposableFunction0 !androidx.compose.runtime.internal  ComposableFunction1 !androidx.compose.runtime.internal  ComposableFunction2 !androidx.compose.runtime.internal  	Alignment androidx.compose.ui  Modifier androidx.compose.ui  Center androidx.compose.ui.Alignment  CenterHorizontally androidx.compose.ui.Alignment  CenterVertically androidx.compose.ui.Alignment  	Companion androidx.compose.ui.Alignment  
Horizontal androidx.compose.ui.Alignment  Vertical androidx.compose.ui.Alignment  Center 'androidx.compose.ui.Alignment.Companion  CenterHorizontally 'androidx.compose.ui.Alignment.Companion  CenterVertically 'androidx.compose.ui.Alignment.Companion  	Companion androidx.compose.ui.Modifier  	clickable androidx.compose.ui.Modifier  clip androidx.compose.ui.Modifier  fillMaxSize androidx.compose.ui.Modifier  fillMaxWidth androidx.compose.ui.Modifier  height androidx.compose.ui.Modifier  padding androidx.compose.ui.Modifier  size androidx.compose.ui.Modifier  fillMaxSize &androidx.compose.ui.Modifier.Companion  fillMaxWidth &androidx.compose.ui.Modifier.Companion  height &androidx.compose.ui.Modifier.Companion  padding &androidx.compose.ui.Modifier.Companion  size &androidx.compose.ui.Modifier.Companion  clip androidx.compose.ui.draw  Color androidx.compose.ui.graphics  ImageVector #androidx.compose.ui.graphics.vector  ContentScale androidx.compose.ui.layout  	Companion 'androidx.compose.ui.layout.ContentScale  Crop 'androidx.compose.ui.layout.ContentScale  Crop 1androidx.compose.ui.layout.ContentScale.Companion  LocalContext androidx.compose.ui.platform  	TextStyle androidx.compose.ui.text  
FontFamily androidx.compose.ui.text.font  
FontWeight androidx.compose.ui.text.font  SystemFontFamily androidx.compose.ui.text.font  	Companion (androidx.compose.ui.text.font.FontFamily  Default (androidx.compose.ui.text.font.FontFamily  Default 2androidx.compose.ui.text.font.FontFamily.Companion  Bold (androidx.compose.ui.text.font.FontWeight  	Companion (androidx.compose.ui.text.font.FontWeight  Normal (androidx.compose.ui.text.font.FontWeight  Bold 2androidx.compose.ui.text.font.FontWeight.Companion  Normal 2androidx.compose.ui.text.font.FontWeight.Companion  Dp androidx.compose.ui.unit  TextUnit androidx.compose.ui.unit  dp androidx.compose.ui.unit  getDp androidx.compose.ui.unit  sp androidx.compose.ui.unit  Bundle #androidx.core.app.ComponentActivity  ImageUploadApp #androidx.core.app.ComponentActivity  MyFirstAppTheme #androidx.core.app.ComponentActivity  
setContent #androidx.core.app.ComponentActivity  FileProvider androidx.core.content  
getUriForFile "androidx.core.content.FileProvider  AsyncImagePainter coil.compose  rememberAsyncImagePainter coil.compose  ActivityResultContracts com.example.myfirstapp  	Alignment com.example.myfirstapp  Arrangement com.example.myfirstapp  Box com.example.myfirstapp  Bundle com.example.myfirstapp  Card com.example.myfirstapp  CardDefaults com.example.myfirstapp  CircularProgressIndicator com.example.myfirstapp  Column com.example.myfirstapp  ComponentActivity com.example.myfirstapp  
Composable com.example.myfirstapp  ContentScale com.example.myfirstapp  Context com.example.myfirstapp  Dispatchers com.example.myfirstapp  	Exception com.example.myfirstapp  ExperimentalMaterial3Api com.example.myfirstapp  File com.example.myfirstapp  FileOutputStream com.example.myfirstapp  FileProvider com.example.myfirstapp  FloatingActionButton com.example.myfirstapp  
FontWeight com.example.myfirstapp  Icon com.example.myfirstapp  Icons com.example.myfirstapp  Image com.example.myfirstapp  ImageUploadApp com.example.myfirstapp  InputStream com.example.myfirstapp  
JSONObject com.example.myfirstapp  
LazyColumn com.example.myfirstapp  MainActivity com.example.myfirstapp  Manifest com.example.myfirstapp  
MaterialTheme com.example.myfirstapp  ModalBottomSheet com.example.myfirstapp  Modifier com.example.myfirstapp  
MultipartBody com.example.myfirstapp  MyFirstAppTheme com.example.myfirstapp  OkHttpClient com.example.myfirstapp  OptIn com.example.myfirstapp  Request com.example.myfirstapp  RoundedCornerShape com.example.myfirstapp  Row com.example.myfirstapp  Scaffold com.example.myfirstapp  SnackbarHost com.example.myfirstapp  SnackbarHostState com.example.myfirstapp  Spacer com.example.myfirstapp  String com.example.myfirstapp  System com.example.myfirstapp  Text com.example.myfirstapp  Unit com.example.myfirstapp  Uri com.example.myfirstapp  
asRequestBody com.example.myfirstapp  
cardElevation com.example.myfirstapp  	clickable com.example.myfirstapp  clip com.example.myfirstapp  copyTo com.example.myfirstapp  
copyUriToFile com.example.myfirstapp  fillMaxSize com.example.myfirstapp  fillMaxWidth com.example.myfirstapp  getValue com.example.myfirstapp  height com.example.myfirstapp  java com.example.myfirstapp  launch com.example.myfirstapp  let com.example.myfirstapp  listOf com.example.myfirstapp  mutableStateOf com.example.myfirstapp  padding com.example.myfirstapp  plus com.example.myfirstapp  println com.example.myfirstapp  provideDelegate com.example.myfirstapp  remember com.example.myfirstapp  rememberAsyncImagePainter com.example.myfirstapp  rememberCoroutineScope com.example.myfirstapp  setValue com.example.myfirstapp  size com.example.myfirstapp  spacedBy com.example.myfirstapp  toMediaTypeOrNull com.example.myfirstapp  uploadImageToCloudinary com.example.myfirstapp  use com.example.myfirstapp  withContext com.example.myfirstapp  ImageUploadApp #com.example.myfirstapp.MainActivity  MyFirstAppTheme #com.example.myfirstapp.MainActivity  
setContent #com.example.myfirstapp.MainActivity  Boolean com.example.myfirstapp.ui.theme  Build com.example.myfirstapp.ui.theme  
Composable com.example.myfirstapp.ui.theme  DarkColorScheme com.example.myfirstapp.ui.theme  
FontFamily com.example.myfirstapp.ui.theme  
FontWeight com.example.myfirstapp.ui.theme  LightColorScheme com.example.myfirstapp.ui.theme  MyFirstAppTheme com.example.myfirstapp.ui.theme  Pink40 com.example.myfirstapp.ui.theme  Pink80 com.example.myfirstapp.ui.theme  Purple40 com.example.myfirstapp.ui.theme  Purple80 com.example.myfirstapp.ui.theme  PurpleGrey40 com.example.myfirstapp.ui.theme  PurpleGrey80 com.example.myfirstapp.ui.theme  
Typography com.example.myfirstapp.ui.theme  Unit com.example.myfirstapp.ui.theme  File java.io  FileOutputStream java.io  IOException java.io  InputStream java.io  
asRequestBody java.io.File  canRead java.io.File  exists java.io.File  length java.io.File  name java.io.File  use java.io.FileOutputStream  copyTo java.io.InputStream  use java.io.InputStream  Class 	java.lang  currentTimeMillis java.lang.System  
BigDecimal 	java.math  
BigInteger 	java.math  TimeUnit java.util.concurrent  SECONDS java.util.concurrent.TimeUnit  Array kotlin  BooleanArray kotlin  	ByteArray kotlin  	CharArray kotlin  DoubleArray kotlin  	Exception kotlin  
FloatArray kotlin  	Function0 kotlin  	Function1 kotlin  IntArray kotlin  	LongArray kotlin  Nothing kotlin  OptIn kotlin  
ShortArray kotlin  
UByteArray kotlin  	UIntArray kotlin  
ULongArray kotlin  UShortArray kotlin  let kotlin  plus kotlin  use kotlin  not kotlin.Boolean  sp 
kotlin.Double  localizedMessage kotlin.Exception  message kotlin.Exception  printStackTrace kotlin.Exception  invoke kotlin.Function0  invoke kotlin.Function1  	compareTo 
kotlin.Int  times 
kotlin.Int  	compareTo kotlin.Long  div kotlin.Long  toMediaTypeOrNull 
kotlin.String  localizedMessage kotlin.Throwable  message kotlin.Throwable  printStackTrace kotlin.Throwable  List kotlin.collections  Map kotlin.collections  Set kotlin.collections  listOf kotlin.collections  plus kotlin.collections  isEmpty kotlin.collections.List  plus kotlin.collections.List  SuspendFunction1 kotlin.coroutines  copyTo 	kotlin.io  println 	kotlin.io  use 	kotlin.io  java 
kotlin.jvm  KMutableProperty0 kotlin.reflect  Sequence kotlin.sequences  plus kotlin.sequences  plus kotlin.text  CoroutineDispatcher kotlinx.coroutines  CoroutineScope kotlinx.coroutines  Dispatchers kotlinx.coroutines  Job kotlinx.coroutines  MainCoroutineDispatcher kotlinx.coroutines  launch kotlinx.coroutines  withContext kotlinx.coroutines  Dispatchers !kotlinx.coroutines.CoroutineScope  File !kotlinx.coroutines.CoroutineScope  FileOutputStream !kotlinx.coroutines.CoroutineScope  
JSONObject !kotlinx.coroutines.CoroutineScope  
MultipartBody !kotlinx.coroutines.CoroutineScope  OkHttpClient !kotlinx.coroutines.CoroutineScope  Request !kotlinx.coroutines.CoroutineScope  System !kotlinx.coroutines.CoroutineScope  
asRequestBody !kotlinx.coroutines.CoroutineScope  copyTo !kotlinx.coroutines.CoroutineScope  
copyUriToFile !kotlinx.coroutines.CoroutineScope  java !kotlinx.coroutines.CoroutineScope  launch !kotlinx.coroutines.CoroutineScope  plus !kotlinx.coroutines.CoroutineScope  println !kotlinx.coroutines.CoroutineScope  toMediaTypeOrNull !kotlinx.coroutines.CoroutineScope  uploadImageToCloudinary !kotlinx.coroutines.CoroutineScope  use !kotlinx.coroutines.CoroutineScope  withContext !kotlinx.coroutines.CoroutineScope  IO kotlinx.coroutines.Dispatchers  Main kotlinx.coroutines.Dispatchers  ActivityResultContracts okhttp3  	Alignment okhttp3  Arrangement okhttp3  Box okhttp3  Bundle okhttp3  Call okhttp3  Card okhttp3  CardDefaults okhttp3  CircularProgressIndicator okhttp3  Column okhttp3  ComponentActivity okhttp3  
Composable okhttp3  ContentScale okhttp3  Context okhttp3  Dispatchers okhttp3  	Exception okhttp3  ExperimentalMaterial3Api okhttp3  File okhttp3  FileOutputStream okhttp3  FileProvider okhttp3  FloatingActionButton okhttp3  
FontWeight okhttp3  Icon okhttp3  Icons okhttp3  Image okhttp3  ImageUploadApp okhttp3  InputStream okhttp3  
JSONObject okhttp3  
LazyColumn okhttp3  Manifest okhttp3  
MaterialTheme okhttp3  	MediaType okhttp3  ModalBottomSheet okhttp3  Modifier okhttp3  
MultipartBody okhttp3  MyFirstAppTheme okhttp3  OkHttpClient okhttp3  OptIn okhttp3  Request okhttp3  RequestBody okhttp3  Response okhttp3  ResponseBody okhttp3  RoundedCornerShape okhttp3  Row okhttp3  Scaffold okhttp3  SnackbarHost okhttp3  SnackbarHostState okhttp3  Spacer okhttp3  String okhttp3  System okhttp3  Text okhttp3  Unit okhttp3  Uri okhttp3  
asRequestBody okhttp3  
cardElevation okhttp3  	clickable okhttp3  clip okhttp3  copyTo okhttp3  
copyUriToFile okhttp3  fillMaxSize okhttp3  fillMaxWidth okhttp3  getValue okhttp3  height okhttp3  java okhttp3  launch okhttp3  let okhttp3  listOf okhttp3  mutableStateOf okhttp3  padding okhttp3  plus okhttp3  println okhttp3  provideDelegate okhttp3  remember okhttp3  rememberAsyncImagePainter okhttp3  rememberCoroutineScope okhttp3  setValue okhttp3  size okhttp3  spacedBy okhttp3  toMediaTypeOrNull okhttp3  uploadImageToCloudinary okhttp3  use okhttp3  withContext okhttp3  execute okhttp3.Call  toMediaTypeOrNull okhttp3.MediaType.Companion  Builder okhttp3.MultipartBody  	Companion okhttp3.MultipartBody  FORM okhttp3.MultipartBody  addFormDataPart okhttp3.MultipartBody.Builder  build okhttp3.MultipartBody.Builder  setType okhttp3.MultipartBody.Builder  FORM okhttp3.MultipartBody.Companion  Builder okhttp3.OkHttpClient  	Companion okhttp3.OkHttpClient  newCall okhttp3.OkHttpClient  build okhttp3.OkHttpClient.Builder  connectTimeout okhttp3.OkHttpClient.Builder  readTimeout okhttp3.OkHttpClient.Builder  writeTimeout okhttp3.OkHttpClient.Builder  Builder okhttp3.Request  	addHeader okhttp3.Request.Builder  build okhttp3.Request.Builder  post okhttp3.Request.Builder  url okhttp3.Request.Builder  
asRequestBody okhttp3.RequestBody.Companion  body okhttp3.Response  code okhttp3.Response  isSuccessful okhttp3.Response  string okhttp3.ResponseBody  
JSONObject org.json  	getString org.json.JSONObject                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       