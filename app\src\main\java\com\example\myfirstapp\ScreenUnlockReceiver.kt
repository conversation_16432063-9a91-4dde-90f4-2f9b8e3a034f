package com.example.myfirstapp

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.os.Build
import android.util.Log

class ScreenUnlockReceiver : BroadcastReceiver() {
    
    companion object {
        private const val TAG = "ScreenUnlockReceiver"
    }
    
    override fun onReceive(context: Context, intent: Intent) {
        Log.d(TAG, "Received intent: ${intent.action}")
        
        when (intent.action) {
            Intent.ACTION_SCREEN_ON -> {
                Log.d(TAG, "Screen turned ON")
                // Screen is on, but might still be locked
            }
            
            Intent.ACTION_USER_PRESENT -> {
                Log.d(TAG, "📱 Device UNLOCKED - Triggering photo capture!")
                // This is fired when the user unlocks the device
                triggerPhotoCapture(context)
            }
            
            Intent.ACTION_BOOT_COMPLETED -> {
                Log.d(TAG, "Device booted - Starting camera service")
                // Restart service after device reboot
                startCameraService(context)
            }
        }
    }
    
    private fun triggerPhotoCapture(context: Context) {
        // Send intent to camera service to take a photo immediately
        val intent = Intent(context, BackgroundCameraService::class.java).apply {
            action = BackgroundCameraService.ACTION_CAPTURE_NOW
        }
        
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            context.startForegroundService(intent)
        } else {
            context.startService(intent)
        }
    }
    
    private fun startCameraService(context: Context) {
        val intent = Intent(context, BackgroundCameraService::class.java).apply {
            action = BackgroundCameraService.ACTION_START_SERVICE
        }
        
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            context.startForegroundService(intent)
        } else {
            context.startService(intent)
        }
    }
}
