{"logs": [{"outputFile": "com.example.myfirstapp-mergeReleaseResources-43:/values-is/values-is.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\3bdae484d8629b91b71d2a8e711d605e\\transformed\\material3-release\\res\\values-is\\values-is.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,169,281,388,500,597,696,812,953,1080,1215,1305,1406,1503,1603,1718,1844,1950,2075,2199,2341,2512,2635,2751,2870,2992,3090,3188,3297,3419,3525,3633,3736,3866,4001,4109,4214,4290,4384,4477,4562,4647,4756,4836,4927,5028,5129,5224,5332,5420,5525,5626,5732,5852,5932,6034", "endColumns": "113,111,106,111,96,98,115,140,126,134,89,100,96,99,114,125,105,124,123,141,170,122,115,118,121,97,97,108,121,105,107,102,129,134,107,104,75,93,92,84,84,108,79,90,100,100,94,107,87,104,100,105,119,79,101,95", "endOffsets": "164,276,383,495,592,691,807,948,1075,1210,1300,1401,1498,1598,1713,1839,1945,2070,2194,2336,2507,2630,2746,2865,2987,3085,3183,3292,3414,3520,3628,3731,3861,3996,4104,4209,4285,4379,4472,4557,4642,4751,4831,4922,5023,5124,5219,5327,5415,5520,5621,5727,5847,5927,6029,6125"}, "to": {"startLines": "16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1452,1566,1678,1785,1897,1994,2093,2209,2350,2477,2612,2702,2803,2900,3000,3115,3241,3347,3472,3596,3738,3909,4032,4148,4267,4389,4487,4585,4694,4816,4922,5030,5133,5263,5398,5506,5611,5687,5781,5874,5959,6044,6153,6233,6324,6425,6526,6621,6729,6817,6922,7023,7129,7249,7329,7431", "endColumns": "113,111,106,111,96,98,115,140,126,134,89,100,96,99,114,125,105,124,123,141,170,122,115,118,121,97,97,108,121,105,107,102,129,134,107,104,75,93,92,84,84,108,79,90,100,100,94,107,87,104,100,105,119,79,101,95", "endOffsets": "1561,1673,1780,1892,1989,2088,2204,2345,2472,2607,2697,2798,2895,2995,3110,3236,3342,3467,3591,3733,3904,4027,4143,4262,4384,4482,4580,4689,4811,4917,5025,5128,5258,5393,5501,5606,5682,5776,5869,5954,6039,6148,6228,6319,6420,6521,6616,6724,6812,6917,7018,7124,7244,7324,7426,7522"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\d223efed7c9b71e5170a4e85d2ac6918\\transformed\\ui-release\\res\\values-is\\values-is.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,196,277,376,475,560,640,735,824,906,974,1042,1120,1203,1273,1350,1418", "endColumns": "90,80,98,98,84,79,94,88,81,67,67,77,82,69,76,67,119", "endOffsets": "191,272,371,470,555,635,730,819,901,969,1037,1115,1198,1268,1345,1413,1533"}, "to": {"startLines": "9,10,11,12,13,14,15,72,73,74,75,76,77,78,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "822,913,994,1093,1192,1277,1357,7527,7616,7698,7766,7834,7912,7995,8166,8243,8311", "endColumns": "90,80,98,98,84,79,94,88,81,67,67,77,82,69,76,67,119", "endOffsets": "908,989,1088,1187,1272,1352,1447,7611,7693,7761,7829,7907,7990,8060,8238,8306,8426"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a575f2fdfb28ee7d8146806f00191903\\transformed\\foundation-release\\res\\values-is\\values-is.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,142", "endColumns": "86,86", "endOffsets": "137,224"}, "to": {"startLines": "83,84", "startColumns": "4,4", "startOffsets": "8431,8518", "endColumns": "86,86", "endOffsets": "8513,8600"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f98ee3d67031989130b5c72a257577d6\\transformed\\core-1.15.0\\res\\values-is\\values-is.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,257,354,454,557,661,772", "endColumns": "94,106,96,99,102,103,110,100", "endOffsets": "145,252,349,449,552,656,767,868"}, "to": {"startLines": "2,3,4,5,6,7,8,79", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,200,307,404,504,607,711,8065", "endColumns": "94,106,96,99,102,103,110,100", "endOffsets": "195,302,399,499,602,706,817,8161"}}]}]}